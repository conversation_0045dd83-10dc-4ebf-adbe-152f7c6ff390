import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faGraduationCap,
  faHandshake,
  faUsers,
  faLaptopCode,
  faChalkboardTeacher,
  faCalendarAlt,
  faComments,
  faBook,
  faVideo,
  faLanguage,
  faRunning,
  faPaintBrush,
  faCode,
  faMusic,
  faFlask,
  faStar,
  faFilter,
  faMapMarkerAlt,
  faGuitar,
  faChess,
  faDumbbell,
  faBrain,
  faChartLine,
  faShieldAlt,
  faServer
} from '@fortawesome/free-solid-svg-icons';

import Hero from '../../components/ui/Hero';
import Section from '../../components/ui/Section';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';

// Import placeholder images
import images from '../../assets/placeholderImages';

// Common styles for all sections
const SectionTitle = styled.h2`
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
`;

const SectionUnderline = styled.div`
  width: 100px;
  height: 4px;
  background-color: #a3e635;
  margin: 0 auto;
  margin-bottom: 3rem;
`;

// Skills Catalog Section
const CatalogContainer = styled.div`
  background-color: #f8f9fa;
  color: var(--text-color);
  padding: 3rem 0;
`;

const CatalogGrid = styled.div`
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
`;

const FiltersContainer = styled.div`
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
`;

const FilterTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
`;

const FilterSubtitle = styled.h4`
  font-size: 1.2rem;
  margin: 1.5rem 0 1rem;
`;

const CategoryList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const CategoryItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
`;

const CategoryIcon = styled.div`
  width: 40px;
  height: 40px;
  background-color: #a3e635;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1e3a4c;
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
`;

const Checkbox = styled.input.attrs({ type: 'checkbox' })`
  width: 18px;
  height: 18px;
`;

const SkillsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const SkillCard = styled.div`
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const SkillImageContainer = styled.div`
  position: relative;
  height: 180px;
  overflow: hidden;
`;

const SkillImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const SkillOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #a3e635;
  opacity: 0.7;
  clip-path: ${props => props.clipPath || 'polygon(0 0, 100% 0, 100% 100%, 0% 100%)'};
`;

const SkillBadge = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #ff8800;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  z-index: 2;
`;

const SkillContent = styled.div`
  padding: 1.5rem;
  background-color: white;
  color: #333;
`;

const SkillTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
  color: #1e3a4c;
`;

const SkillLocation = styled.p`
  color: #666;
  margin-bottom: 1rem;
  font-size: 0.9rem;
`;

const SkillFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
`;

const SkillCount = styled.span`
  color: #666;
`;

const SkillRating = styled.span`
  color: #ffb400;
  font-weight: bold;
`;

// Skills Stories Section
const StoriesContainer = styled.div`
  background-color: #f8f9fa;
  color: var(--text-color);
  padding: 3rem 0;
`;

const StoriesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const StoryCard = styled.div`
  background-color: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const StoryHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
`;

const StoryIcon = styled.div`
  width: 50px;
  height: 50px;
  background-color: #a3e635;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: #1e3a4c;
`;

const StoryTitle = styled.h3`
  font-size: 1.2rem;
  color: var(--primary-color);
`;

const StoryContent = styled.p`
  flex: 1;
  margin-bottom: 1.5rem;
  line-height: 1.6;
`;

const StoryFooter = styled.div`
  margin-top: auto;
`;

const StoryAuthor = styled.h4`
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
`;

const StoryLocation = styled.p`
  color: #a3e635;
  font-size: 0.9rem;
`;

// Popular Pairs Section
const PairsContainer = styled.div`
  background-color: #f8f9fa;
  color: var(--text-color);
  padding: 3rem 0;
`;

const PairsGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const PairRow = styled.div`
  background-color: #ffffff;
  border-radius: 8px;
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const PairItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
`;

const PairIcon = styled.div`
  width: 70px;
  height: 70px;
  background-color: #4b1d89;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: #5fe3e4;
  font-size: 1.5rem;
`;

const PairTitle = styled.h3`
  font-size: 1.2rem;
  color: var(--dark-color);
`;

// User Rankings Section
const RankingsContainer = styled.div`
  background-color: #f8f9fa;
  color: var(--text-color);
  padding: 3rem 0;
`;

const RankingsTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
`;

const TableHeader = styled.thead`
  background-color: var(--primary-color);
  text-align: left;

  th {
    padding: 1rem;
    font-weight: 600;
    color: white;
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid var(--border-color);

    &:last-child {
      border-bottom: none;
    }
  }

  td {
    padding: 1rem;
    color: var(--text-color);
  }
`;

const RankNumber = styled.td`
  color: var(--primary-color);
  font-weight: bold;
`;

const StarRating = styled.div`
  display: flex;
  color: #ffb400;
`;

// Skills Statistics Section
const StatsContainer = styled.div`
  background-color: #f8f9fa;
  color: var(--text-color);
  padding: 3rem 0;
`;

const PopularSkillCard = styled.div`
  background-color: #ffffff;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const PopularSkillHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const PopularSkillIcon = styled.div`
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.5rem;
  color: white;
`;

const PopularSkillInfo = styled.div`
  flex: 1;
`;

const PopularSkillTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
`;

const PopularSkillGrowth = styled.p`
  color: var(--success-color);
  font-size: 0.9rem;
`;

const ProgressBar = styled.div`
  height: 8px;
  background-color: var(--border-color);
  border-radius: 4px;
  margin-top: 1rem;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: ${props => props.progress || '0%'};
    background-color: var(--primary-color);
    border-radius: 4px;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const StatCard = styled.div`
  background-color: #ffffff;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const StatValue = styled.div`
  font-size: 3rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 1.1rem;
  color: var(--text-light);
`;

const Services = () => {
  // Sample data for the skills catalog
  const skills = [
    {
      id: 1,
      title: 'Artificial Intelligence',
      location: 'Karachi',
      count: 327,
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1677442135136-760c813dce26',
      badge: 'TRENDING',
      clipPath: 'polygon(0 0, 100% 0, 100% 100%, 0% 100%)'
    },
    {
      id: 2,
      title: 'Data Science',
      location: 'Lahore',
      count: 456,
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71',
      clipPath: 'polygon(0 0, 100% 0, 70% 100%, 0% 100%)'
    },
    {
      id: 3,
      title: 'Cybersecurity',
      location: 'Islamabad',
      count: 213,
      rating: 4.3,
      image: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3',
      clipPath: 'polygon(0 0, 100% 0, 100% 100%, 30% 100%)'
    },
    {
      id: 4,
      title: 'Cloud Computing',
      location: 'Faisalabad',
      count: 378,
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1544197150-b99a580bb7a8',
      badge: 'TRENDING',
      clipPath: 'polygon(0 0, 100% 0, 70% 100%, 0% 100%)'
    },
    {
      id: 5,
      title: 'DevOps',
      location: 'Rawalpindi',
      count: 189,
      rating: 4.2,
      image: 'https://images.unsplash.com/photo-1607799279861-4dd421887fb3',
      clipPath: 'polygon(30% 0, 100% 0, 100% 100%, 0% 100%)'
    },
    {
      id: 6,
      title: 'Blockchain & Web3',
      location: 'Multan',
      count: 248,
      rating: 4.4,
      image: 'https://images.unsplash.com/photo-1639762681057-408e52192e55',
      clipPath: 'polygon(0 0, 100% 0, 100% 100%, 30% 100%)'
    },
    {
      id: 7,
      title: 'React.js',
      location: 'Peshawar',
      count: 156,
      rating: 4.1,
      image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee',
      clipPath: 'polygon(0 0, 70% 0, 100% 100%, 0% 100%)'
    },
    {
      id: 8,
      title: 'Product Management',
      location: 'Quetta',
      count: 287,
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f',
      clipPath: 'polygon(30% 0, 100% 0, 100% 100%, 0% 100%)'
    },
    {
      id: 9,
      title: 'Effective Communication',
      location: 'Hyderabad',
      count: 234,
      rating: 4.3,
      image: 'https://images.unsplash.com/photo-1557804506-669a67965ba0',
      badge: 'NEW',
      clipPath: 'polygon(0 0, 100% 0, 70% 100%, 30% 100%)'
    }
  ];

  // Sample data for skills stories
  const stories = [
    {
      id: 1,
      title: 'Python Programming',
      content: 'Thanks to skill exchange, I was able to learn Python in just 6 months while teaching graphic design to my exchange partner.',
      author: 'Ali Ahmed',
      location: 'Karachi',
      icon: faCode
    },
    {
      id: 2,
      title: 'Data Science',
      content: 'I learned data science by exchanging knowledge about web development. Now I work on exciting data projects every day!',
      author: 'Fatima Khan',
      location: 'Lahore',
      icon: faChartLine
    },
    {
      id: 3,
      title: 'Web Development',
      content: 'I always dreamed of building websites. Through exchanging my design skills, I found an excellent React.js mentor.',
      author: 'Hassan Malik',
      location: 'Islamabad',
      icon: faLaptopCode
    }
  ];

  // Sample data for popular pairs
  const popularPairs = [
    { id: 1, title: 'React.js', icon: faCode },
    { id: 2, title: 'Data Science', icon: faChartLine },
    { id: 3, title: 'Cybersecurity', icon: faShieldAlt },
    { id: 4, title: 'Cloud Computing', icon: faServer }
  ];

  // Sample data for user rankings
  const userRankings = [
    { id: 1, rank: 1, name: 'Ahmed Khan', city: 'Karachi', skill: 'Artificial Intelligence', rating: 5 },
    { id: 2, rank: 2, name: 'Sara Ali', city: 'Lahore', skill: 'Data Science', rating: 5 },
    { id: 3, rank: 3, name: 'Bilal Ahmad', city: 'Islamabad', skill: 'React.js', rating: 4.9 },
    { id: 4, rank: 4, name: 'Ayesha Malik', city: 'Faisalabad', skill: 'Cloud Computing', rating: 4.8 },
    { id: 5, rank: 5, name: 'Usman Farooq', city: 'Rawalpindi', skill: 'DevOps', rating: 4.7 }
  ];

  return (
    <>
      <Hero
        bgImage={images.heroServices}
        title="Our Services"
        subtitle="Discover the various ways SkillSwap can help you learn new skills or share your expertise with others."
        height="60vh"
      />

      {/* Skills Catalog Section */}
      <Section bgColor="#f8f9fa">
        <CatalogContainer>
          <SectionTitle>Skills Catalog</SectionTitle>
          <SectionUnderline />

          <CatalogGrid>
            {/* Filters */}
            <FiltersContainer>
              <FilterTitle>Filters</FilterTitle>

              <FilterSubtitle>Categories</FilterSubtitle>
              <CategoryList>
                <CategoryItem>
                  <CategoryIcon>
                    <FontAwesomeIcon icon={faLanguage} />
                  </CategoryIcon>
                  <span>Languages</span>
                </CategoryItem>

                <CategoryItem>
                  <CategoryIcon>
                    <FontAwesomeIcon icon={faRunning} />
                  </CategoryIcon>
                  <span>Sports</span>
                </CategoryItem>

                <CategoryItem>
                  <CategoryIcon>
                    <FontAwesomeIcon icon={faPaintBrush} />
                  </CategoryIcon>
                  <span>Art</span>
                </CategoryItem>

                <CategoryItem>
                  <CategoryIcon>
                    <FontAwesomeIcon icon={faCode} />
                  </CategoryIcon>
                  <span>Programming</span>
                </CategoryItem>

                <CategoryItem>
                  <CategoryIcon>
                    <FontAwesomeIcon icon={faMusic} />
                  </CategoryIcon>
                  <span>Music</span>
                </CategoryItem>

                <CategoryItem>
                  <CategoryIcon>
                    <FontAwesomeIcon icon={faFlask} />
                  </CategoryIcon>
                  <span>Science</span>
                </CategoryItem>
              </CategoryList>

              <FilterSubtitle>Popularity</FilterSubtitle>
              <CheckboxContainer>
                <Checkbox id="most-popular" />
                <label htmlFor="most-popular">Most Popular</label>
              </CheckboxContainer>

              <CheckboxContainer>
                <Checkbox id="new" />
                <label htmlFor="new">New</label>
              </CheckboxContainer>

              <CheckboxContainer>
                <Checkbox id="trending" />
                <label htmlFor="trending">Trending</label>
              </CheckboxContainer>

              <div style={{ marginTop: '2rem', fontSize: '0.9rem', color: '#a3e635' }}>
                Found: 15 skills
              </div>
            </FiltersContainer>

            {/* Skills Grid */}
            <SkillsGrid>
              {skills.map(skill => (
                <SkillCard key={skill.id}>
                  {skill.badge && <SkillBadge>{skill.badge}</SkillBadge>}
                  <SkillImageContainer>
                    <SkillImage src={`${skill.image}?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&q=80`} alt={skill.title} />
                    <SkillOverlay clipPath={skill.clipPath} />
                  </SkillImageContainer>
                  <SkillContent>
                    <SkillTitle>{skill.title}</SkillTitle>
                    <SkillLocation>
                      <FontAwesomeIcon icon={faMapMarkerAlt} style={{ marginRight: '0.5rem' }} />
                      {skill.location}
                    </SkillLocation>
                    <SkillFooter>
                      <SkillCount>{skill.count}</SkillCount>
                      <SkillRating>
                        {[...Array(Math.floor(skill.rating))].map((_, i) => (
                          <FontAwesomeIcon key={i} icon={faStar} style={{ marginRight: '0.25rem' }} />
                        ))}
                        {skill.rating % 1 !== 0 && (
                          <FontAwesomeIcon icon={faStar} style={{ marginRight: '0.25rem', opacity: 0.5 }} />
                        )}
                      </SkillRating>
                    </SkillFooter>
                  </SkillContent>
                </SkillCard>
              ))}
            </SkillsGrid>
          </CatalogGrid>
        </CatalogContainer>
      </Section>

      {/* Skills Stories Section */}
      <Section bgColor="#f8f9fa">
        <StoriesContainer>
          <SectionTitle>Skills Stories</SectionTitle>
          <SectionUnderline />

          <StoriesGrid>
            {stories.map(story => (
              <StoryCard key={story.id}>
                <StoryHeader>
                  <StoryIcon>
                    <FontAwesomeIcon icon={story.icon} />
                  </StoryIcon>
                  <StoryTitle>"{story.title}"</StoryTitle>
                </StoryHeader>

                <StoryContent>
                  {story.content}
                </StoryContent>

                <StoryFooter>
                  <StoryAuthor>{story.author}</StoryAuthor>
                  <StoryLocation>{story.location}</StoryLocation>
                </StoryFooter>
              </StoryCard>
            ))}
          </StoriesGrid>
        </StoriesContainer>
      </Section>

      {/* Popular Pairs Section */}
      <Section bgColor="#f8f9fa">
        <PairsContainer>
          <SectionTitle>Popular Pairs</SectionTitle>
          <SectionUnderline />

          <PairsGrid>
            <PairRow>
              {popularPairs.slice(0, 2).map(pair => (
                <PairItem key={pair.id}>
                  <PairIcon>
                    <FontAwesomeIcon icon={pair.icon} />
                  </PairIcon>
                  <PairTitle>{pair.title}</PairTitle>
                </PairItem>
              ))}
            </PairRow>

            <PairRow>
              {popularPairs.slice(2, 4).map(pair => (
                <PairItem key={pair.id}>
                  <PairIcon>
                    <FontAwesomeIcon icon={pair.icon} />
                  </PairIcon>
                  <PairTitle>{pair.title}</PairTitle>
                </PairItem>
              ))}
            </PairRow>
          </PairsGrid>
        </PairsContainer>
      </Section>

      {/* User Rankings Section */}
      <Section bgColor="#f8f9fa">
        <RankingsContainer>
          <SectionTitle>User Rankings</SectionTitle>
          <SectionUnderline />

          <RankingsTable>
            <TableHeader>
              <tr>
                <th>RANK</th>
                <th>NAME</th>
                <th>CITY</th>
                <th>SKILL</th>
                <th>RATING</th>
              </tr>
            </TableHeader>
            <TableBody>
              {userRankings.map(user => (
                <tr key={user.id}>
                  <RankNumber>{user.rank}</RankNumber>
                  <td>{user.name}</td>
                  <td>{user.city}</td>
                  <td>{user.skill}</td>
                  <td>
                    <StarRating>
                      {[...Array(Math.floor(user.rating))].map((_, i) => (
                        <FontAwesomeIcon key={i} icon={faStar} style={{ marginRight: '0.25rem' }} />
                      ))}
                    </StarRating>
                  </td>
                </tr>
              ))}
            </TableBody>
          </RankingsTable>
        </RankingsContainer>
      </Section>

      {/* Skills Statistics Section */}
      <Section bgColor="#f8f9fa">
        <StatsContainer>
          <SectionTitle>Skills Statistics</SectionTitle>
          <SectionUnderline />

          <PopularSkillCard>
            <h3 style={{ marginBottom: '1.5rem' }}>Most Popular Skill of the Month</h3>
            <PopularSkillHeader>
              <PopularSkillIcon>
                <FontAwesomeIcon icon={faBrain} style={{ fontSize: '1.5rem', color: '#a3e635' }} />
              </PopularSkillIcon>
              <PopularSkillInfo>
                <PopularSkillTitle>Artificial Intelligence</PopularSkillTitle>
                <PopularSkillGrowth>+32% growth in popularity</PopularSkillGrowth>
              </PopularSkillInfo>
            </PopularSkillHeader>
            <ProgressBar progress="75%" />
          </PopularSkillCard>

          <StatsGrid>
            <StatCard>
              <StatValue>2,845</StatValue>
              <StatLabel>Total Active Skills</StatLabel>
            </StatCard>

            <StatCard>
              <StatValue>342</StatValue>
              <StatLabel>New Skills This Month</StatLabel>
            </StatCard>

            <StatCard>
              <StatValue>12,567</StatValue>
              <StatLabel>Completed Exchanges</StatLabel>
            </StatCard>
          </StatsGrid>
        </StatsContainer>
      </Section>
    </>
  );
};

export default Services;
