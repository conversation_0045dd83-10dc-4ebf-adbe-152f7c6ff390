import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faMapMarkerAlt,
  faPhone,
  faEnvelope,
  faCheck
} from '@fortawesome/free-solid-svg-icons';

import Hero from '../../components/ui/Hero';
import Section from '../../components/ui/Section';
import Button from '../../components/ui/Button';

// Import placeholder images
import images from '../../assets/placeholderImages';

const ContactContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
`;

const ContactInfo = styled.div`
  @media (max-width: 992px) {
    order: 2;
  }
`;

const ContactForm = styled.div`
  background-color: #f8f9fa;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);

  @media (max-width: 992px) {
    order: 1;
  }
`;

const FormTitle = styled.h3`
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-lg);
`;

const FormGroup = styled.div`
  margin-bottom: var(--spacing-lg);
`;

const FormLabel = styled.label`
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
`;

const FormInput = styled.input`
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-md);
  transition: all 0.3s ease;
  background-color: white;

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(126, 87, 194, 0.2);
  }
`;

const FormTextarea = styled.textarea`
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-md);
  transition: all 0.3s ease;
  min-height: 150px;
  resize: vertical;
  background-color: white;

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(126, 87, 194, 0.2);
  }
`;

const InfoItem = styled.div`
  display: flex;
  margin-bottom: var(--spacing-lg);
  background-color: #f8f9fa;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
`;

const InfoIcon = styled.div`
  width: 50px;
  height: 50px;
  background-color: white;
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const InfoContent = styled.div`
  h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
  }

  p {
    color: var(--text-light);
  }
`;

const FAQContainer = styled.div`
  margin-top: var(--spacing-3xl);
`;

const FAQItem = styled.div`
  margin-bottom: var(--spacing-lg);
  background-color: #f8f9fa;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
`;

const FAQQuestion = styled.h4`
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--dark-color);
  font-weight: 600;
`;

const FAQAnswer = styled.p`
  color: var(--text-light);
`;

const SuccessMessage = styled.div`
  background-color: var(--success-color);
  color: white;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);

  svg {
    margin-right: var(--spacing-md);
    font-size: 1.5rem;
  }
`;

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the form data to your backend
    console.log('Form submitted:', formData);
    setIsSubmitted(true);
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: ''
    });

    // Reset the success message after 5 seconds
    setTimeout(() => {
      setIsSubmitted(false);
    }, 5000);
  };

  return (
    <>
      <Hero
        bgImage={images.heroContact}
        title="Contact Us"
        subtitle="Get in touch with our team for any questions, feedback, or support."
        height="60vh"
      />

      <Section>
        <ContactContainer>
          <ContactForm>
            <FormTitle>Send Us a Message</FormTitle>

            {isSubmitted && (
              <SuccessMessage>
                <FontAwesomeIcon icon={faCheck} />
                <span>Your message has been sent successfully! We'll get back to you soon.</span>
              </SuccessMessage>
            )}

            <form onSubmit={handleSubmit}>
              <FormGroup>
                <FormLabel htmlFor="name">Your Name</FormLabel>
                <FormInput
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <FormLabel htmlFor="email">Your Email</FormLabel>
                <FormInput
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <FormLabel htmlFor="subject">Subject</FormLabel>
                <FormInput
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <FormLabel htmlFor="message">Your Message</FormLabel>
                <FormTextarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                />
              </FormGroup>

              <Button
                type="submit"
                size="large"
                style={{
                  backgroundColor: '#f1f1f1',
                  color: 'var(--dark-color)',
                  border: '1px solid #e0e0e0',
                  padding: '0.8rem 1.5rem',
                  borderRadius: '50px',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#e0e0e0';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = '#f1f1f1';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >Send Message</Button>
            </form>
          </ContactForm>

          <ContactInfo>
            <h2>Get in Touch</h2>
            <p>
              Have questions about SkillSwap? Want to learn more about our services or provide feedback?
              We'd love to hear from you! Reach out to us using any of the methods below.
            </p>

            <div style={{ marginTop: 'var(--spacing-xl)' }}>
              <InfoItem>
                <InfoIcon>
                  <FontAwesomeIcon icon={faMapMarkerAlt} />
                </InfoIcon>
                <InfoContent>
                  <h4>Our Location</h4>
                  <p>Rashidi hostel MUET, Jamshoro</p>
                </InfoContent>
              </InfoItem>

              <InfoItem>
                <InfoIcon>
                  <FontAwesomeIcon icon={faPhone} />
                </InfoIcon>
                <InfoContent>
                  <h4>Phone Number</h4>
                  <p>03065805656</p>
                </InfoContent>
              </InfoItem>

              <InfoItem>
                <InfoIcon>
                  <FontAwesomeIcon icon={faEnvelope} />
                </InfoIcon>
                <InfoContent>
                  <h4>Email Address</h4>
                  <p><EMAIL></p>
                </InfoContent>
              </InfoItem>
            </div>

            <FAQContainer>
              <h3>Frequently Asked Questions</h3>

              <FAQItem>
                <FAQQuestion>How do I sign up for SkillSwap?</FAQQuestion>
                <FAQAnswer>
                  You can sign up by clicking the "Sign Up" button in the top right corner of our website.
                  Follow the prompts to create your account and set up your profile.
                </FAQAnswer>
              </FAQItem>

              <FAQItem>
                <FAQQuestion>How do I access the admin panel?</FAQQuestion>
                <FAQAnswer>
                  You can access the admin panel by clicking the "Login" button and then selecting "Login as Admin".
                  Use the username "admin" and password "admin12" to log in as an administrator.
                </FAQAnswer>
              </FAQItem>

              <FAQItem>
                <FAQQuestion>How do I become an expert on SkillSwap?</FAQQuestion>
                <FAQAnswer>
                  To become an expert, you need to apply through our "Become an Expert" page.
                  We'll review your application and get back to you within 48 hours.
                </FAQAnswer>
              </FAQItem>

              <FAQItem>
                <FAQQuestion>What payment methods do you accept?</FAQQuestion>
                <FAQAnswer>
                  We accept all major credit cards, PayPal, and bank transfers for our services.
                </FAQAnswer>
              </FAQItem>
            </FAQContainer>
          </ContactInfo>
        </ContactContainer>
      </Section>

      <Section
        title="Rashidi hostel MUET, Jamshoro"
        bgColor="var(--background-color)"
        spacing="lg"
      >
        <div style={{ height: '400px', borderRadius: 'var(--border-radius-md)', overflow: 'hidden' }}>
          {/* Replace with an actual map component or embed a Google Maps iframe */}
          <div style={{
            width: '100%',
            height: '100%',
            backgroundColor: '#e9e9e9',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <p>Map will be displayed here</p>
          </div>
        </div>
      </Section>
    </>
  );
};

export default Contact;
