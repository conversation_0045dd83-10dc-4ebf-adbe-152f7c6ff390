/**
 * Admin Module - Centralized Exports
 *
 * This file serves as the main entry point for all admin-related components,
 * pages, and utilities. It follows industry standards for module organization
 * and provides both named and default exports for flexibility.
 */

// Layout Components
export { default as AdminLayout } from '../components/admin/AdminLayout';

// Page Components
export { default as Users } from '../pages/admin/Users';
export { default as Skills } from '../pages/admin/Skills';
export { default as Exchanges } from '../pages/admin/Exchanges';
export { default as AdminDashboard } from '../pages/admin/AdminDashboard';
export { default as UserManagement } from '../pages/admin/UserManagement';

// UI Components
export {
  AdminCard,
  AdminTable,
  AdminStats,
  AdminUIComponents
} from './components';

// Hooks
export { useAdminData } from './hooks/useAdminData';

// Utils
export { default as adminHelpers } from './utils/adminHelpers';
export * from './utils/adminHelpers';

// Grouped exports for convenience
export const AdminComponents = {
  Layout: () => import('../components/admin/AdminLayout'),
  Card: () => import('./components/AdminCard'),
  Table: () => import('./components/AdminTable'),
  Stats: () => import('./components/AdminStats'),
};

export const AdminPages = {
  Users: () => import('../pages/admin/Users'),
  Skills: () => import('../pages/admin/Skills'),
  Exchanges: () => import('../pages/admin/Exchanges'),
  Dashboard: () => import('../pages/admin/AdminDashboard'),
  UserManagement: () => import('../pages/admin/UserManagement'),
};

// Main admin routes configuration
export const adminRoutes = [
  {
    path: '/admin',
    component: 'AdminLayout',
    children: [
      { path: '', component: 'Users' },
      { path: 'users', component: 'Users' },
      { path: 'skills', component: 'Skills' },
      { path: 'exchanges', component: 'Exchanges' },
      { path: 'dashboard', component: 'AdminDashboard' },
    ]
  }
];

// Admin permissions and roles
export const adminPermissions = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MODERATOR: 'moderator',
};

export const adminRoles = {
  [adminPermissions.SUPER_ADMIN]: {
    name: 'Super Administrator',
    permissions: ['*'],
  },
  [adminPermissions.ADMIN]: {
    name: 'Administrator',
    permissions: ['users.manage', 'skills.manage', 'exchanges.manage'],
  },
  [adminPermissions.MODERATOR]: {
    name: 'Moderator',
    permissions: ['skills.moderate', 'exchanges.moderate'],
  },
};

// Default export for main admin module
export default {
  Components: AdminComponents,
  Pages: AdminPages,
  Routes: adminRoutes,
  Permissions: adminPermissions,
  Roles: adminRoles,
};
