import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faFilter,
  faEdit,
  faTrash,
  faEye,
  faPlus,
  faSort,
  faSortUp,
  faSortDown,
  faCheckCircle,
  faTimesCircle,
  faGraduationCap,
  faCode,
  faPalette,
  faMusic,
  faLanguage,
  faHeartbeat,
  faUtensils,
  faChartLine
} from '@fortawesome/free-solid-svg-icons';

const Skills = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [sortField, setSortField] = useState('title');
  const [sortDirection, setSortDirection] = useState('asc');

  const categories = [
    { id: 'all', name: 'All Skills', icon: faGraduationCap, color: '#6366f1' },
    { id: 'programming', name: 'Programming', icon: faCode, color: '#3b82f6' },
    { id: 'design', name: 'Design', icon: faPalette, color: '#ec4899' },
    { id: 'music', name: 'Music', icon: faMusic, color: '#8b5cf6' },
    { id: 'language', name: 'Languages', icon: faLanguage, color: '#10b981' },
    { id: 'fitness', name: 'Fitness', icon: faHeartbeat, color: '#ef4444' },
    { id: 'cooking', name: 'Cooking', icon: faUtensils, color: '#f59e0b' },
    { id: 'business', name: 'Business', icon: faChartLine, color: '#06b6d4' }
  ];

  const skills = [
    {
      id: 1,
      title: 'React Development',
      category: 'programming',
      instructor: 'John Smith',
      students: 25,
      rating: 4.8,
      status: 'Active',
      createdDate: '2023-01-15',
      thumbnail: 'https://via.placeholder.com/60x60?text=React'
    },
    {
      id: 2,
      title: 'UI/UX Design',
      category: 'design',
      instructor: 'Emily Davis',
      students: 18,
      rating: 4.9,
      status: 'Active',
      createdDate: '2023-02-20',
      thumbnail: 'https://via.placeholder.com/60x60?text=Design'
    },
    {
      id: 3,
      title: 'Guitar Lessons',
      category: 'music',
      instructor: 'Michael Johnson',
      students: 12,
      rating: 4.7,
      status: 'Active',
      createdDate: '2023-03-10',
      thumbnail: 'https://via.placeholder.com/60x60?text=Guitar'
    },
    {
      id: 4,
      title: 'Spanish Conversation',
      category: 'language',
      instructor: 'Sarah Wilson',
      students: 30,
      rating: 4.6,
      status: 'Pending',
      createdDate: '2023-04-05',
      thumbnail: 'https://via.placeholder.com/60x60?text=Spanish'
    },
    {
      id: 5,
      title: 'Yoga for Beginners',
      category: 'fitness',
      instructor: 'David Brown',
      students: 22,
      rating: 4.8,
      status: 'Active',
      createdDate: '2023-05-12',
      thumbnail: 'https://via.placeholder.com/60x60?text=Yoga'
    }
  ];

  const filteredSkills = activeCategory === 'all' 
    ? skills 
    : skills.filter(skill => skill.category === activeCategory);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field) => {
    if (sortField !== field) {
      return <FontAwesomeIcon icon={faSort} className="text-gray-400" />;
    }
    return sortDirection === 'asc' 
      ? <FontAwesomeIcon icon={faSortUp} className="text-blue-600" />
      : <FontAwesomeIcon icon={faSortDown} className="text-blue-600" />;
  };

  const getStatusBadge = (status) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (status.toLowerCase()) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'inactive':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">Skills Management</h1>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
          <FontAwesomeIcon icon={faPlus} />
          Add New Skill
        </button>
      </div>
      
      {/* Filter Section */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-md">
          <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input 
            type="text" 
            placeholder="Search skills..." 
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
          <FontAwesomeIcon icon={faFilter} />
          Filters
        </button>
      </div>
      
      {/* Categories */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => setActiveCategory(category.id)}
            className={`
              px-4 py-2 rounded-lg flex items-center gap-2 transition-all text-sm font-medium
              ${activeCategory === category.id 
                ? 'bg-blue-600 text-white shadow-md' 
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }
            `}
          >
            <FontAwesomeIcon icon={category.icon} />
            {category.name}
          </button>
        ))}
      </div>
      
      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th 
                  onClick={() => handleSort('title')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Skill
                    {getSortIcon('title')}
                  </div>
                </th>
                <th 
                  onClick={() => handleSort('instructor')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Instructor
                    {getSortIcon('instructor')}
                  </div>
                </th>
                <th 
                  onClick={() => handleSort('students')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Students
                    {getSortIcon('students')}
                  </div>
                </th>
                <th 
                  onClick={() => handleSort('rating')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Rating
                    {getSortIcon('rating')}
                  </div>
                </th>
                <th 
                  onClick={() => handleSort('status')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Status
                    {getSortIcon('status')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredSkills.map(skill => (
                <tr key={skill.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        <img 
                          className="h-12 w-12 rounded-lg object-cover" 
                          src={skill.thumbnail} 
                          alt={skill.title} 
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{skill.title}</div>
                        <div className="text-sm text-gray-500 capitalize">{skill.category}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">{skill.instructor}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">{skill.students}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-900">{skill.rating}</span>
                      <span className="text-yellow-400 ml-1">★</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getStatusBadge(skill.status)}>
                      {skill.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button className="text-blue-600 hover:text-blue-900 p-1" title="View">
                        <FontAwesomeIcon icon={faEye} />
                      </button>
                      <button className="text-green-600 hover:text-green-900 p-1" title="Edit">
                        <FontAwesomeIcon icon={faEdit} />
                      </button>
                      <button className="text-red-600 hover:text-red-900 p-1" title="Delete">
                        <FontAwesomeIcon icon={faTrash} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Skills;
