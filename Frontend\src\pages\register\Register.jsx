import { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faUpload, faImage } from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';

// Styled Components
const RegisterContainer = styled.div`
  background-color: #212121; // Dark background color
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`;

const RegisterTitle = styled.h1`
  color: white;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
`;

const RegisterForm = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid #e0e0e0;
`;

const Tab = styled.button`
  flex: 1;
  padding: 1rem;
  background-color: ${props => props.active ? 'white' : '#f5f5f5'};
  border: none;
  font-weight: ${props => props.active ? '600' : '400'};
  color: ${props => props.active ? '#4285f4' : '#757575'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.active ? 'white' : '#eeeeee'};
  }

  &:focus {
    outline: none;
  }
`;

const FormContent = styled.div`
  padding: 2rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  color: #5fe3e4;
  font-size: 0.9rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #5fe3e4;
    box-shadow: 0 0 0 2px rgba(95, 227, 228, 0.2);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 1rem;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;

  &:focus {
    outline: none;
    border-color: #5fe3e4;
    box-shadow: 0 0 0 2px rgba(95, 227, 228, 0.2);
  }
`;

const AddButton = styled.button`
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background-color: #3367d6;
  }

  &:focus {
    outline: none;
  }
`;

const SkillsContainer = styled.div`
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const ImageUploadContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
`;

const ImagePreviewContainer = styled.div`
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px dashed #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  position: relative;
`;

const ImagePreview = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const UploadButton = styled.button`
  background-color: #5fe3e4;
  color: #212121;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background-color: #4fd0d1;
  }

  &:focus {
    outline: none;
  }
`;

const HiddenInput = styled.input`
  display: none;
`;

const SkillTag = styled.div`
  background-color: #e0e0e0;
  color: #424242;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  button {
    background: none;
    border: none;
    color: #757575;
    cursor: pointer;
    padding: 0;
    font-size: 0.8rem;

    &:hover {
      color: #f44336;
    }
  }
`;

const ButtonsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
  }
`;

const PrimaryButton = styled(Button)`
  background-color: #5fe3e4;
  color: #212121;

  &:hover {
    background-color: #4fd0d1;
  }
`;

const SecondaryButton = styled(Button)`
  background-color: transparent;
  color: #757575;

  &:hover {
    background-color: #f5f5f5;
  }
`;

// Available skills for dropdown
const availableSkills = [
  'JavaScript', 'Python', 'React', 'Node.js', 'HTML/CSS',
  'UI/UX Design', 'Graphic Design', 'Digital Marketing',
  'Content Writing', 'Photography', 'Video Editing',
  'Cooking', 'Yoga', 'Meditation', 'Public Speaking',
  'Language Learning', 'Music Production', 'Drawing',
  'Data Science', 'Machine Learning'
];

const Register = () => {
  const navigate = useNavigate();
  const { login, currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState('registration');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    profileImage: null,
    linkedinLink: '',
    githubLink: '',
    portfolioLink: '',
    skillsToTeach: [],
    skillsToLearn: [],
    educationLevel: '',
    majorFieldOfStudy: '',
    universityName: '',
    educationDescription: '',
    currentStatus: '',
    bio: '',
    state: '',
    country: '',
    timezone: ''
  });
  const [imagePreview, setImagePreview] = useState(null);
  const fileInputRef = useRef(null);
  const [selectedSkill, setSelectedSkill] = useState('');
  const [selectedSkillToLearn, setSelectedSkillToLearn] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageClick = () => {
    fileInputRef.current.click();
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
        setFormData(prev => ({
          ...prev,
          profileImage: file
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const addSkill = () => {
    if (selectedSkill && !formData.skillsToTeach.includes(selectedSkill)) {
      setFormData(prev => ({
        ...prev,
        skillsToTeach: [...prev.skillsToTeach, selectedSkill]
      }));
      setSelectedSkill('');
    }
  };

  const removeSkill = (skill) => {
    setFormData(prev => ({
      ...prev,
      skillsToTeach: prev.skillsToTeach.filter(s => s !== skill)
    }));
  };

  const addSkillToLearn = () => {
    if (selectedSkillToLearn && !formData.skillsToLearn.includes(selectedSkillToLearn)) {
      setFormData(prev => ({
        ...prev,
        skillsToLearn: [...prev.skillsToLearn, selectedSkillToLearn]
      }));
      setSelectedSkillToLearn('');
    }
  };

  const removeSkillToLearn = (skill) => {
    setFormData(prev => ({
      ...prev,
      skillsToLearn: prev.skillsToLearn.filter(s => s !== skill)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);

    // Create a user object with the form data
    const userData = {
      username: formData.email.split('@')[0], // Generate a username from email
      name: formData.name,
      email: formData.email,
      avatar: imagePreview || 'https://randomuser.me/api/portraits/lego/1.jpg', // Default avatar if none provided
      isAuthenticated: true,
      // Include all registration form data
      ...formData,
      // Add additional fields needed for profile display
      rating: 5,
      ratingCount: 0,
      id: formData.email.split('@')[0] // Use the same ID format as expected in profile
    };

    // Save the user data in the auth context
    login(userData);

    // Redirect to the profile page
    navigate('/profile');
  };

  return (
    <RegisterContainer>
      <RegisterTitle>Community Registration</RegisterTitle>
      <div style={{ color: 'white', textAlign: 'center', marginBottom: '2rem', maxWidth: '600px' }}>
        <p>Complete your profile and join the SkillSwap community by registering your skills and preferences.</p>
        <p style={{ marginTop: '0.5rem', fontSize: '0.9rem' }}><i>Already have an account? This form is for existing users to complete their profile.</i></p>
      </div>

      <RegisterForm>
        <TabsContainer>
          <Tab
            active={activeTab === 'registration'}
            onClick={() => setActiveTab('registration')}
          >
            Registration
          </Tab>
          <Tab
            active={activeTab === 'education'}
            onClick={() => setActiveTab('education')}
          >
            Education
          </Tab>
          <Tab
            active={activeTab === 'additional'}
            onClick={() => setActiveTab('additional')}
          >
            Additional
          </Tab>
          <Tab
            active={activeTab === 'confirm'}
            onClick={() => setActiveTab('confirm')}
          >
            Confirm Details
          </Tab>
        </TabsContainer>

        <FormContent>
          {activeTab === 'registration' && (
            <form onSubmit={(e) => e.preventDefault()}>
              <FormGroup>
                <Label>Name</Label>
                <Input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter your full name"
                />
              </FormGroup>

              <FormGroup>
                <Label>Email</Label>
                <Input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter your email address"
                />
              </FormGroup>

              <FormGroup>
                <Label>Profile Image</Label>
                <ImageUploadContainer>
                  <ImagePreviewContainer onClick={handleImageClick}>
                    {imagePreview ? (
                      <ImagePreview src={imagePreview} alt="Profile Preview" />
                    ) : (
                      <FontAwesomeIcon icon={faImage} size="3x" color="#bdbdbd" />
                    )}
                  </ImagePreviewContainer>
                  <UploadButton type="button" onClick={handleImageClick}>
                    <FontAwesomeIcon icon={faUpload} />
                    {imagePreview ? 'Change Image' : 'Upload Image'}
                  </UploadButton>
                  <HiddenInput
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageChange}
                    accept="image/*"
                  />
                  <small style={{ color: '#757575', textAlign: 'center' }}>
                    Upload a profile picture (recommended size: 300x300 pixels)
                  </small>
                </ImageUploadContainer>
              </FormGroup>

              <FormGroup>
                <Label>LinkedIn Link</Label>
                <Input
                  type="url"
                  name="linkedinLink"
                  value={formData.linkedinLink}
                  onChange={handleInputChange}
                  placeholder="Enter your LinkedIn link"
                />
              </FormGroup>

              <FormGroup>
                <Label>Github Link</Label>
                <Input
                  type="url"
                  name="githubLink"
                  value={formData.githubLink}
                  onChange={handleInputChange}
                  placeholder="Enter your Github link"
                />
              </FormGroup>

              <FormGroup>
                <Label>Portfolio Link</Label>
                <Input
                  type="url"
                  name="portfolioLink"
                  value={formData.portfolioLink}
                  onChange={handleInputChange}
                  placeholder="Enter your portfolio link"
                />
              </FormGroup>

              <FormGroup>
                <Label>Skills to Teach</Label>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <Select
                    value={selectedSkill}
                    onChange={(e) => setSelectedSkill(e.target.value)}
                  >
                    <option value="">Select some skill</option>
                    {availableSkills.map((skill) => (
                      <option key={skill} value={skill}>{skill}</option>
                    ))}
                  </Select>
                  <AddButton type="button" onClick={addSkill}>
                    <FontAwesomeIcon icon={faPlus} /> Add Skill
                  </AddButton>
                </div>

                <SkillsContainer>
                  {formData.skillsToTeach.map((skill) => (
                    <SkillTag key={skill}>
                      {skill}
                      <button type="button" onClick={() => removeSkill(skill)}>×</button>
                    </SkillTag>
                  ))}
                </SkillsContainer>
              </FormGroup>

              <FormGroup>
                <Label>Skills To Learn</Label>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <Select
                    value={selectedSkillToLearn}
                    onChange={(e) => setSelectedSkillToLearn(e.target.value)}
                  >
                    <option value="">Select some skill</option>
                    {availableSkills.map((skill) => (
                      <option key={skill} value={skill}>{skill}</option>
                    ))}
                  </Select>
                  <AddButton type="button" onClick={addSkillToLearn}>
                    <FontAwesomeIcon icon={faPlus} /> Add Skill
                  </AddButton>
                </div>

                <SkillsContainer>
                  {formData.skillsToLearn.map((skill) => (
                    <SkillTag key={skill}>
                      {skill}
                      <button type="button" onClick={() => removeSkillToLearn(skill)}>×</button>
                    </SkillTag>
                  ))}
                </SkillsContainer>
              </FormGroup>

              <ButtonsContainer>
                <SecondaryButton type="button">Cancel</SecondaryButton>
                <PrimaryButton
                  type="button"
                  onClick={() => setActiveTab('education')}
                >
                  Next
                </PrimaryButton>
              </ButtonsContainer>
            </form>
          )}

          {activeTab === 'education' && (
            <form onSubmit={(e) => e.preventDefault()}>
              <FormGroup>
                <Label>Education Level</Label>
                <Select
                  name="educationLevel"
                  value={formData.educationLevel}
                  onChange={handleInputChange}
                >
                  <option value="">Select your education level</option>
                  <option value="High School">High School</option>
                  <option value="Bachelor's Degree">Bachelor's Degree</option>
                  <option value="Master's Degree">Master's Degree</option>
                  <option value="Ph.D">Ph.D</option>
                  <option value="Other">Other</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>Major Field of Study</Label>
                <Input
                  type="text"
                  name="majorFieldOfStudy"
                  value={formData.majorFieldOfStudy}
                  onChange={handleInputChange}
                  placeholder="Enter your major field of study"
                />
              </FormGroup>

              <FormGroup>
                <Label>University Name</Label>
                <Input
                  type="text"
                  name="universityName"
                  value={formData.universityName}
                  onChange={handleInputChange}
                  placeholder="Enter your university or institution name"
                />
              </FormGroup>

              <FormGroup>
                <Label>Description</Label>
                <textarea
                  name="educationDescription"
                  value={formData.educationDescription}
                  onChange={handleInputChange}
                  placeholder="Describe your education background, major, achievements, etc."
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px',
                    fontSize: '1rem',
                    minHeight: '120px',
                    resize: 'vertical',
                    fontFamily: 'inherit'
                  }}
                />
              </FormGroup>

              <ButtonsContainer>
                <SecondaryButton
                  type="button"
                  onClick={() => setActiveTab('registration')}
                >
                  Back
                </SecondaryButton>
                <PrimaryButton
                  type="button"
                  onClick={() => setActiveTab('additional')}
                >
                  Next
                </PrimaryButton>
              </ButtonsContainer>
            </form>
          )}

          {activeTab === 'additional' && (
            <form onSubmit={(e) => e.preventDefault()}>
              <FormGroup>
                <Label>Current Status</Label>
                <Select
                  name="currentStatus"
                  value={formData.currentStatus}
                  onChange={handleInputChange}
                >
                  <option value="">Select your current status</option>
                  <option value="Student">Student</option>
                  <option value="Employed">Employed</option>
                  <option value="Self-employed">Self-employed</option>
                  <option value="Freelancer">Freelancer</option>
                  <option value="Unemployed">Unemployed</option>
                  <option value="Retired">Retired</option>
                  <option value="Other">Other</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>Bio</Label>
                <textarea
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  placeholder="Tell us about yourself, your interests, and what motivates you"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px',
                    fontSize: '1rem',
                    minHeight: '120px',
                    resize: 'vertical',
                    fontFamily: 'inherit'
                  }}
                />
              </FormGroup>

              <FormGroup>
                <Label>State/Province</Label>
                <Input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  placeholder="Enter your state or province"
                />
              </FormGroup>

              <FormGroup>
                <Label>Country</Label>
                <Input
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  placeholder="Enter your country"
                />
              </FormGroup>

              <FormGroup>
                <Label>Time Zone</Label>
                <Select
                  name="timezone"
                  value={formData.timezone}
                  onChange={handleInputChange}
                >
                  <option value="">Select your time zone</option>
                  <option value="UTC-12:00">(UTC-12:00) International Date Line West</option>
                  <option value="UTC-11:00">(UTC-11:00) Coordinated Universal Time-11</option>
                  <option value="UTC-10:00">(UTC-10:00) Hawaii</option>
                  <option value="UTC-09:00">(UTC-09:00) Alaska</option>
                  <option value="UTC-08:00">(UTC-08:00) Pacific Time (US & Canada)</option>
                  <option value="UTC-07:00">(UTC-07:00) Mountain Time (US & Canada)</option>
                  <option value="UTC-06:00">(UTC-06:00) Central Time (US & Canada)</option>
                  <option value="UTC-05:00">(UTC-05:00) Eastern Time (US & Canada)</option>
                  <option value="UTC-04:00">(UTC-04:00) Atlantic Time (Canada)</option>
                  <option value="UTC-03:00">(UTC-03:00) Brasilia</option>
                  <option value="UTC-02:00">(UTC-02:00) Coordinated Universal Time-02</option>
                  <option value="UTC-01:00">(UTC-01:00) Azores</option>
                  <option value="UTC+00:00">(UTC+00:00) London, Dublin, Edinburgh</option>
                  <option value="UTC+01:00">(UTC+01:00) Berlin, Paris, Rome, Madrid</option>
                  <option value="UTC+02:00">(UTC+02:00) Athens, Istanbul, Helsinki</option>
                  <option value="UTC+03:00">(UTC+03:00) Moscow, St. Petersburg</option>
                  <option value="UTC+04:00">(UTC+04:00) Dubai, Abu Dhabi</option>
                  <option value="UTC+05:00">(UTC+05:00) Islamabad, Karachi</option>
                  <option value="UTC+05:30">(UTC+05:30) New Delhi, Mumbai, Chennai</option>
                  <option value="UTC+06:00">(UTC+06:00) Dhaka</option>
                  <option value="UTC+07:00">(UTC+07:00) Bangkok, Jakarta</option>
                  <option value="UTC+08:00">(UTC+08:00) Beijing, Hong Kong, Singapore</option>
                  <option value="UTC+09:00">(UTC+09:00) Tokyo, Seoul</option>
                  <option value="UTC+10:00">(UTC+10:00) Sydney, Melbourne</option>
                  <option value="UTC+11:00">(UTC+11:00) Vladivostok</option>
                  <option value="UTC+12:00">(UTC+12:00) Auckland, Wellington</option>
                </Select>
              </FormGroup>

              <ButtonsContainer>
                <SecondaryButton
                  type="button"
                  onClick={() => setActiveTab('education')}
                >
                  Back
                </SecondaryButton>
                <PrimaryButton
                  type="button"
                  onClick={() => setActiveTab('confirm')}
                >
                  Next
                </PrimaryButton>
              </ButtonsContainer>
            </form>
          )}

          {activeTab === 'confirm' && (
            <div>
              <h3>Confirm Your Details</h3>
              <p>Please review your information before submitting.</p>
              <div>
                <h4>Personal Information</h4>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
                  {imagePreview && (
                    <div style={{ width: '80px', height: '80px', borderRadius: '50%', overflow: 'hidden' }}>
                      <img src={imagePreview} alt="Profile" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                    </div>
                  )}
                  <div>
                    <p><strong>Name:</strong> {formData.name}</p>
                    <p><strong>Email:</strong> {formData.email}</p>
                  </div>
                </div>
                <p><strong>LinkedIn:</strong> {formData.linkedinLink || 'Not provided'}</p>
                <p><strong>GitHub:</strong> {formData.githubLink || 'Not provided'}</p>
                <p><strong>Portfolio:</strong> {formData.portfolioLink || 'Not provided'}</p>
              </div>

              <div>
                <h4>Education</h4>
                <p><strong>Education Level:</strong> {formData.educationLevel}</p>
                <p><strong>Major Field of Study:</strong> {formData.majorFieldOfStudy}</p>
                <p><strong>University:</strong> {formData.universityName}</p>
                <p><strong>Description:</strong> {formData.educationDescription}</p>
              </div>

              <div>
                <h4>Additional Information</h4>
                <p><strong>Current Status:</strong> {formData.currentStatus}</p>
                <p><strong>Bio:</strong> {formData.bio}</p>
                <p><strong>Location:</strong> {formData.state}, {formData.country}</p>
                <p><strong>Time Zone:</strong> {formData.timezone}</p>
              </div>

              <div>
                <h4>Skills</h4>
                <p><strong>Skills to Teach:</strong> {formData.skillsToTeach.join(', ')}</p>
                <p><strong>Skills To Learn:</strong> {formData.skillsToLearn.join(', ')}</p>
              </div>

              <ButtonsContainer>
                <SecondaryButton
                  type="button"
                  onClick={() => setActiveTab('additional')}
                >
                  Back
                </SecondaryButton>
                <PrimaryButton
                  type="button"
                  onClick={handleSubmit}
                >
                  Submit
                </PrimaryButton>
              </ButtonsContainer>
            </div>
          )}
        </FormContent>
      </RegisterForm>
    </RegisterContainer>
  );
};

export default Register;
