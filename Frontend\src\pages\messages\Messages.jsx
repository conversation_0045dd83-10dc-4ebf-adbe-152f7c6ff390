import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPaperPlane,
  faSearch,
  faEllipsisV,
  faPhone,
  faVideo,
  faArrowLeft,
  faSmile,
  faPaperclip,
  faImage,
  faUserCircle
} from '@fortawesome/free-solid-svg-icons';

// Styled Components
const MessagesContainer = styled.div`
  display: flex;
  height: calc(100vh - 80px);
  background-color: #121212;
  color: white;
`;

const Sidebar = styled.div`
  width: 350px;
  background-color: #212121;
  border-right: 1px solid #333;
  display: flex;
  flex-direction: column;
  
  @media (max-width: 768px) {
    width: ${props => props.showSidebar ? '350px' : '0'};
    position: absolute;
    z-index: 10;
    height: 100%;
    transition: width 0.3s ease;
  }
`;

const SidebarHeader = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid #333;
`;

const SidebarTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: white;
`;

const SearchBar = styled.div`
  display: flex;
  align-items: center;
  background-color: #333;
  border-radius: 50px;
  padding: 0.5rem 1rem;
  
  svg {
    color: #999;
    margin-right: 0.5rem;
  }
  
  input {
    background: transparent;
    border: none;
    color: white;
    width: 100%;
    
    &:focus {
      outline: none;
    }
    
    &::placeholder {
      color: #999;
    }
  }
`;

const ConversationList = styled.div`
  flex: 1;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #212121;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 3px;
  }
`;

const ConversationItem = styled.div`
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #333;
  cursor: pointer;
  transition: background-color 0.2s;
  background-color: ${props => props.active ? '#2a2a2a' : 'transparent'};
  
  &:hover {
    background-color: #2a2a2a;
  }
`;

const Avatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1rem;
  flex-shrink: 0;
`;

const AvatarImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ConversationInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const ConversationName = styled.div`
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ConversationTime = styled.span`
  font-size: 0.75rem;
  color: #999;
  font-weight: normal;
`;

const ConversationPreview = styled.div`
  color: #bbb;
  font-size: 0.85rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ChatArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #121212;
`;

const ChatHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #333;
  background-color: #212121;
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: #5fe3e4;
  font-size: 1.2rem;
  cursor: pointer;
  margin-right: 1rem;
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const ChatInfo = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
`;

const ChatName = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
  margin-left: 1rem;
`;

const ChatStatus = styled.div`
  font-size: 0.8rem;
  color: #5fe3e4;
  margin-left: 1rem;
`;

const ChatActions = styled.div`
  display: flex;
  gap: 1.5rem;
  
  svg {
    color: #999;
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.2s;
    
    &:hover {
      color: #5fe3e4;
    }
  }
`;

const ChatMessages = styled.div`
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #121212;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 3px;
  }
`;

const MessageGroup = styled.div`
  display: flex;
  flex-direction: column;
  align-items: ${props => props.sent ? 'flex-end' : 'flex-start'};
  max-width: 80%;
  align-self: ${props => props.sent ? 'flex-end' : 'flex-start'};
`;

const MessageBubble = styled.div`
  background-color: ${props => props.sent ? '#004d40' : '#333'};
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  border-bottom-right-radius: ${props => props.sent ? '4px' : '18px'};
  border-bottom-left-radius: ${props => !props.sent ? '4px' : '18px'};
  margin-bottom: 0.25rem;
  max-width: 100%;
  word-wrap: break-word;
`;

const MessageTime = styled.div`
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.25rem;
`;

const DateDivider = styled.div`
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  
  &::before, &::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #333;
  }
  
  span {
    padding: 0 1rem;
    color: #999;
    font-size: 0.8rem;
  }
`;

const ChatInput = styled.div`
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #212121;
  border-top: 1px solid #333;
`;

const InputActions = styled.div`
  display: flex;
  gap: 1rem;
  margin-right: 1rem;
  
  svg {
    color: #999;
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.2s;
    
    &:hover {
      color: #5fe3e4;
    }
  }
`;

const MessageInput = styled.input`
  flex: 1;
  background-color: #333;
  border: none;
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  color: white;
  
  &:focus {
    outline: none;
  }
  
  &::placeholder {
    color: #999;
  }
`;

const SendButton = styled.button`
  background-color: #5fe3e4;
  color: #004d40;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(95, 227, 228, 0.5);
  }
  
  &:disabled {
    background-color: #333;
    color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  text-align: center;
  padding: 2rem;
  
  svg {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: #333;
  }
  
  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #bbb;
  }
  
  p {
    max-width: 400px;
    line-height: 1.6;
  }
`;

// Sample data
const conversations = [
  {
    id: 'paakhi108',
    name: 'Paakhi Maheshwari',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    lastMessage: 'I can help you with Python and Machine Learning',
    time: '10:30 AM',
    online: true,
    messages: [
      { id: 1, text: 'Hi there! I saw your profile and I\'m interested in learning Python.', sent: false, time: '10:15 AM' },
      { id: 2, text: 'Hello! I\'d be happy to help you with Python. What\'s your current level of experience?', sent: true, time: '10:20 AM' },
      { id: 3, text: 'I\'m a complete beginner. I have some programming experience with JavaScript but never tried Python.', sent: false, time: '10:25 AM' },
      { id: 4, text: 'That\'s a good starting point! I can help you with Python and Machine Learning. When would you like to start?', sent: true, time: '10:30 AM' }
    ]
  },
  {
    id: 'anil42',
    name: 'Anil Khosla',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    lastMessage: 'Let me know when you\'re free to discuss',
    time: 'Yesterday',
    online: false,
    messages: [
      { id: 1, text: 'Hello, I\'m interested in learning more about Algebra.', sent: true, time: 'Yesterday, 3:45 PM' },
      { id: 2, text: 'Hi there! I\'d be happy to teach you. What specific areas are you interested in?', sent: false, time: 'Yesterday, 4:00 PM' },
      { id: 3, text: 'I\'m particularly interested in linear algebra and its applications.', sent: true, time: 'Yesterday, 4:10 PM' },
      { id: 4, text: 'Great choice! Let me know when you\'re free to discuss and we can set up a session.', sent: false, time: 'Yesterday, 4:15 PM' }
    ]
  },
  {
    id: 'ravi75',
    name: 'Ravi Sharma',
    avatar: 'https://randomuser.me/api/portraits/men/68.jpg',
    lastMessage: 'Thanks for the React tips!',
    time: 'Monday',
    online: true,
    messages: [
      { id: 1, text: 'Hi Ravi, I\'m working on a React project and could use some guidance.', sent: true, time: 'Monday, 2:30 PM' },
      { id: 2, text: 'Hey! I\'d be happy to help. What are you working on?', sent: false, time: 'Monday, 2:45 PM' },
      { id: 3, text: 'I\'m building a dashboard and struggling with state management.', sent: true, time: 'Monday, 3:00 PM' },
      { id: 4, text: 'Have you tried using Redux or Context API? I can show you some examples.', sent: false, time: 'Monday, 3:15 PM' },
      { id: 5, text: 'Thanks for the React tips!', sent: true, time: 'Monday, 3:30 PM' }
    ]
  },
  {
    id: 'priya23',
    name: 'Priya Patel',
    avatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    lastMessage: 'The UI design looks great!',
    time: 'Last week',
    online: false,
    messages: [
      { id: 1, text: 'Hi Priya, I\'d love to learn more about UI design.', sent: true, time: 'Last week' },
      { id: 2, text: 'Hello! I\'d be happy to share my knowledge. Are you interested in any specific area?', sent: false, time: 'Last week' },
      { id: 3, text: 'I\'m particularly interested in mobile app interfaces.', sent: true, time: 'Last week' },
      { id: 4, text: 'That\'s my specialty! I can show you some examples of my work.', sent: false, time: 'Last week' },
      { id: 5, text: 'The UI design looks great!', sent: true, time: 'Last week' }
    ]
  }
];

const Messages = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [activeConversation, setActiveConversation] = useState(null);
  const [message, setMessage] = useState('');
  const [showSidebar, setShowSidebar] = useState(true);
  const messagesEndRef = useRef(null);
  
  useEffect(() => {
    // If userId is provided, set the active conversation
    if (userId) {
      const conversation = conversations.find(c => c.id === userId);
      if (conversation) {
        setActiveConversation(conversation);
        // On mobile, hide sidebar when conversation is selected
        if (window.innerWidth <= 768) {
          setShowSidebar(false);
        }
      }
    } else if (conversations.length > 0) {
      // Default to first conversation if no userId is provided
      setActiveConversation(conversations[0]);
    }
  }, [userId]);
  
  useEffect(() => {
    // Scroll to bottom of messages
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [activeConversation]);
  
  const handleSendMessage = () => {
    if (message.trim() === '') return;
    
    // In a real app, this would send the message to an API
    // For now, we'll just update the local state
    const newMessage = {
      id: activeConversation.messages.length + 1,
      text: message,
      sent: true,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
    
    setActiveConversation(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage],
      lastMessage: message,
      time: 'Just now'
    }));
    
    setMessage('');
  };
  
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };
  
  const selectConversation = (conversation) => {
    setActiveConversation(conversation);
    navigate(`/messages/${conversation.id}`);
    
    // On mobile, hide sidebar when conversation is selected
    if (window.innerWidth <= 768) {
      setShowSidebar(false);
    }
  };
  
  const toggleSidebar = () => {
    setShowSidebar(prev => !prev);
  };
  
  return (
    <MessagesContainer>
      <Sidebar showSidebar={showSidebar}>
        <SidebarHeader>
          <SidebarTitle>Messages</SidebarTitle>
          <SearchBar>
            <FontAwesomeIcon icon={faSearch} />
            <input type="text" placeholder="Search messages..." />
          </SearchBar>
        </SidebarHeader>
        
        <ConversationList>
          {conversations.map(conversation => (
            <ConversationItem 
              key={conversation.id} 
              active={activeConversation?.id === conversation.id}
              onClick={() => selectConversation(conversation)}
            >
              <Avatar>
                <AvatarImage src={conversation.avatar} alt={conversation.name} />
              </Avatar>
              
              <ConversationInfo>
                <ConversationName>
                  {conversation.name}
                  <ConversationTime>{conversation.time}</ConversationTime>
                </ConversationName>
                <ConversationPreview>{conversation.lastMessage}</ConversationPreview>
              </ConversationInfo>
            </ConversationItem>
          ))}
        </ConversationList>
      </Sidebar>
      
      <ChatArea>
        {activeConversation ? (
          <>
            <ChatHeader>
              <BackButton onClick={toggleSidebar}>
                <FontAwesomeIcon icon={faArrowLeft} />
              </BackButton>
              
              <ChatInfo>
                <Avatar>
                  <AvatarImage src={activeConversation.avatar} alt={activeConversation.name} />
                </Avatar>
                <ChatName>{activeConversation.name}</ChatName>
                {activeConversation.online && <ChatStatus>Online</ChatStatus>}
              </ChatInfo>
              
              <ChatActions>
                <FontAwesomeIcon icon={faPhone} />
                <FontAwesomeIcon icon={faVideo} />
                <FontAwesomeIcon icon={faEllipsisV} />
              </ChatActions>
            </ChatHeader>
            
            <ChatMessages>
              <DateDivider>
                <span>Today</span>
              </DateDivider>
              
              {activeConversation.messages.map(msg => (
                <MessageGroup key={msg.id} sent={msg.sent}>
                  <MessageBubble sent={msg.sent}>{msg.text}</MessageBubble>
                  <MessageTime>{msg.time}</MessageTime>
                </MessageGroup>
              ))}
              
              <div ref={messagesEndRef} />
            </ChatMessages>
            
            <ChatInput>
              <InputActions>
                <FontAwesomeIcon icon={faSmile} />
                <FontAwesomeIcon icon={faPaperclip} />
                <FontAwesomeIcon icon={faImage} />
              </InputActions>
              
              <MessageInput 
                type="text" 
                placeholder="Type a message..." 
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
              />
              
              <SendButton 
                disabled={message.trim() === ''}
                onClick={handleSendMessage}
              >
                <FontAwesomeIcon icon={faPaperPlane} />
              </SendButton>
            </ChatInput>
          </>
        ) : (
          <EmptyState>
            <FontAwesomeIcon icon={faUserCircle} />
            <h3>Select a conversation</h3>
            <p>Choose a conversation from the list or start a new one to begin messaging.</p>
          </EmptyState>
        )}
      </ChatArea>
    </MessagesContainer>
  );
};

export default Messages;
