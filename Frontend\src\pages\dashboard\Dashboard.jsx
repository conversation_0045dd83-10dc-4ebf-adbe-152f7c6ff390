import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBell,
  faCalendarAlt,
  faUsers,
  faChalkboardTeacher,
  faGraduationCap,
  faStar,
  faCheckCircle,
  faTimesCircle,
  faEye,
  faChevronRight,
  faComments
} from '@fortawesome/free-solid-svg-icons';

import Button from '../../components/ui/Button';

// Styled Components
const DashboardContainer = styled.div`
  background-color: #f8f9fa;
  min-height: calc(100vh - 80px);
  padding: 2rem;
`;

const DashboardHeader = styled.div`
  margin-bottom: 2rem;
`;

const DashboardTitle = styled.h1`
  font-size: 2.5rem;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
`;

const DashboardSubtitle = styled.p`
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 1.5rem;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }
`;

const StatIcon = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background-color: ${props => props.bgColor || 'var(--primary-color)'};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 1rem;
  color: var(--text-light);
`;

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
`;

const DashboardSection = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.25rem;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const SectionLink = styled(Link)`
  color: var(--primary-color);
  font-size: 0.9rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  &:hover {
    text-decoration: underline;
  }
`;

const NotificationList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const NotificationItem = styled.div`
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  background-color: ${props => props.unread ? 'rgba(var(--primary-rgb), 0.05)' : 'transparent'};
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
  }
`;

const NotificationAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1rem;
  flex-shrink: 0;
`;

const NotificationImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationTitle = styled.div`
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.25rem;
`;

const NotificationTime = styled.div`
  font-size: 0.8rem;
  color: var(--text-light);
`;

const NotificationActions = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
`;

const ActionButton = styled.button`
  background-color: ${props => props.accept ? 'var(--success-color)' : props.reject ? 'var(--danger-color)' : 'var(--primary-color)'};
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
`;

const MeetingList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const MeetingItem = styled.div`
  display: flex;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
  }
`;

const MeetingDate = styled.div`
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
`;

const MeetingDay = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
`;

const MeetingMonth = styled.div`
  font-size: 0.8rem;
  text-transform: uppercase;
`;

const MeetingContent = styled.div`
  flex: 1;
`;

const MeetingTitle = styled.div`
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.25rem;
`;

const MeetingDetails = styled.div`
  font-size: 0.9rem;
  color: var(--text-color);
  margin-bottom: 0.5rem;
`;

const MeetingTime = styled.div`
  font-size: 0.8rem;
  color: var(--text-light);
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const SkillsList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const SkillTag = styled.div`
  background-color: #f1f1f1;
  color: var(--dark-color);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  border: 1px solid #e0e0e0;
  transition: transform 0.2s ease, background-color 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    background-color: #e0e0e0;
  }
`;

const FeedbackList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FeedbackItem = styled.div`
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
  }
`;

const FeedbackHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
`;

const FeedbackAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.75rem;
`;

const FeedbackImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const FeedbackUser = styled.div`
  flex: 1;
`;

const FeedbackName = styled.div`
  font-weight: 600;
  color: var(--dark-color);
`;

const FeedbackDate = styled.div`
  font-size: 0.8rem;
  color: var(--text-light);
`;

const FeedbackRating = styled.div`
  display: flex;
  color: #ffb400;
  font-size: 0.9rem;
`;

const FeedbackContent = styled.div`
  font-size: 0.9rem;
  color: var(--text-color);
  line-height: 1.5;
`;

const ConnectionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const ConnectionItem = styled.div`
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
  }
`;

const ConnectionAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.75rem;
`;

const ConnectionImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ConnectionInfo = styled.div`
  flex: 1;
`;

const ConnectionName = styled.div`
  font-weight: 600;
  color: var(--dark-color);
`;

const ConnectionSkill = styled.div`
  font-size: 0.8rem;
  color: var(--text-light);
`;

// Sample data for the dashboard
const sampleNotifications = [
  {
    id: 1,
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    name: 'Ahmad Khan',
    action: 'sent you a connection request',
    time: '2 hours ago',
    unread: true
  },
  {
    id: 2,
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    name: 'Fatima Ahmed',
    action: 'accepted your connection request',
    time: '1 day ago',
    unread: false
  },
  {
    id: 3,
    avatar: 'https://randomuser.me/api/portraits/men/68.jpg',
    name: 'Ali Hassan',
    action: 'wants to schedule a meeting',
    time: '2 days ago',
    unread: false
  }
];

const sampleMeetings = [
  {
    id: 1,
    day: '15',
    month: 'Jun',
    title: 'React.js Basics',
    with: 'Ali Hassan',
    time: '10:00 AM - 11:30 AM',
    status: 'upcoming'
  },
  {
    id: 2,
    day: '18',
    month: 'Jun',
    title: 'Data Science Introduction',
    with: 'Fatima Ahmed',
    time: '2:00 PM - 3:30 PM',
    status: 'upcoming'
  },
  {
    id: 3,
    day: '22',
    month: 'Jun',
    title: 'Cybersecurity Fundamentals',
    with: 'Usman Farooq',
    time: '11:00 AM - 12:30 PM',
    status: 'upcoming'
  }
];

const sampleFeedback = [
  {
    id: 1,
    avatar: 'https://randomuser.me/api/portraits/men/68.jpg',
    name: 'Ali Hassan',
    date: 'June 10, 2023',
    rating: 5,
    content: 'Great teacher! Explained React concepts very clearly and was patient with my questions.'
  },
  {
    id: 2,
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    name: 'Fatima Ahmed',
    date: 'May 28, 2023',
    rating: 4,
    content: 'Very knowledgeable about data science. The session was informative and practical.'
  }
];

const sampleConnections = [
  {
    id: 1,
    avatar: 'https://randomuser.me/api/portraits/men/68.jpg',
    name: 'Ali Hassan',
    skill: 'React.js Developer'
  },
  {
    id: 2,
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    name: 'Fatima Ahmed',
    skill: 'Data Scientist'
  },
  {
    id: 3,
    avatar: 'https://randomuser.me/api/portraits/men/42.jpg',
    name: 'Usman Farooq',
    skill: 'Cybersecurity Expert'
  },
  {
    id: 4,
    avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
    name: 'Zainab Qureshi',
    skill: 'Urdu Literature Professor'
  }
];

const Dashboard = () => {
  const { currentUser } = useAuth();
  const [notifications, setNotifications] = useState(sampleNotifications);
  const [meetings, setMeetings] = useState(sampleMeetings);
  const [feedback, setFeedback] = useState(sampleFeedback);
  const [connections, setConnections] = useState(sampleConnections);
  const [stats, setStats] = useState({
    notifications: 1,
    meetings: 3,
    connections: 4,
    skillsTaught: 5,
    skillsLearned: 3,
    feedback: 2
  });

  const handleAcceptRequest = (id) => {
    // In a real app, this would make an API call
    setNotifications(prev => prev.map(notification => 
      notification.id === id 
        ? { ...notification, unread: false, action: 'request accepted' } 
        : notification
    ));
    setStats(prev => ({ ...prev, connections: prev.connections + 1 }));
  };

  const handleRejectRequest = (id) => {
    // In a real app, this would make an API call
    setNotifications(prev => prev.filter(notification => notification.id !== id));
    setStats(prev => ({ ...prev, notifications: prev.notifications - 1 }));
  };

  return (
    <DashboardContainer>
      <DashboardHeader>
        <DashboardTitle>Dashboard</DashboardTitle>
        <DashboardSubtitle>Welcome back, {currentUser?.name || 'User'}! Here's what's happening with your account.</DashboardSubtitle>
      </DashboardHeader>

      <StatsContainer>
        <StatCard>
          <StatIcon bgColor="var(--primary-color)">
            <FontAwesomeIcon icon={faBell} />
          </StatIcon>
          <StatValue>{stats.notifications}</StatValue>
          <StatLabel>New Notifications</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon bgColor="#4CAF50">
            <FontAwesomeIcon icon={faCalendarAlt} />
          </StatIcon>
          <StatValue>{stats.meetings}</StatValue>
          <StatLabel>Upcoming Meetings</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon bgColor="#2196F3">
            <FontAwesomeIcon icon={faUsers} />
          </StatIcon>
          <StatValue>{stats.connections}</StatValue>
          <StatLabel>Connections</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon bgColor="#FF9800">
            <FontAwesomeIcon icon={faStar} />
          </StatIcon>
          <StatValue>{stats.feedback}</StatValue>
          <StatLabel>New Feedback</StatLabel>
        </StatCard>
      </StatsContainer>

      <DashboardGrid>
        <div>
          <DashboardSection>
            <SectionHeader>
              <SectionTitle>
                <FontAwesomeIcon icon={faBell} />
                Notifications
              </SectionTitle>
              <SectionLink to="/notifications">
                View All <FontAwesomeIcon icon={faChevronRight} />
              </SectionLink>
            </SectionHeader>

            <NotificationList>
              {notifications.map(notification => (
                <NotificationItem key={notification.id} unread={notification.unread}>
                  <NotificationAvatar>
                    <NotificationImage src={notification.avatar} alt={notification.name} />
                  </NotificationAvatar>
                  <NotificationContent>
                    <NotificationTitle>
                      <strong>{notification.name}</strong> {notification.action}
                    </NotificationTitle>
                    <NotificationTime>{notification.time}</NotificationTime>
                  </NotificationContent>
                  {notification.action === 'sent you a connection request' && (
                    <NotificationActions>
                      <ActionButton 
                        accept 
                        title="Accept"
                        onClick={() => handleAcceptRequest(notification.id)}
                      >
                        <FontAwesomeIcon icon={faCheckCircle} />
                      </ActionButton>
                      <ActionButton 
                        reject 
                        title="Reject"
                        onClick={() => handleRejectRequest(notification.id)}
                      >
                        <FontAwesomeIcon icon={faTimesCircle} />
                      </ActionButton>
                    </NotificationActions>
                  )}
                  {notification.action !== 'sent you a connection request' && (
                    <NotificationActions>
                      <ActionButton title="View">
                        <FontAwesomeIcon icon={faEye} />
                      </ActionButton>
                    </NotificationActions>
                  )}
                </NotificationItem>
              ))}
            </NotificationList>
          </DashboardSection>

          <DashboardSection>
            <SectionHeader>
              <SectionTitle>
                <FontAwesomeIcon icon={faCalendarAlt} />
                Upcoming Meetings
              </SectionTitle>
              <SectionLink to="/schedule">
                View All <FontAwesomeIcon icon={faChevronRight} />
              </SectionLink>
            </SectionHeader>

            <MeetingList>
              {meetings.map(meeting => (
                <MeetingItem key={meeting.id}>
                  <MeetingDate>
                    <MeetingDay>{meeting.day}</MeetingDay>
                    <MeetingMonth>{meeting.month}</MeetingMonth>
                  </MeetingDate>
                  <MeetingContent>
                    <MeetingTitle>{meeting.title}</MeetingTitle>
                    <MeetingDetails>With {meeting.with}</MeetingDetails>
                    <MeetingTime>
                      <FontAwesomeIcon icon={faCalendarAlt} />
                      {meeting.time}
                    </MeetingTime>
                  </MeetingContent>
                </MeetingItem>
              ))}
            </MeetingList>
          </DashboardSection>

          <DashboardSection>
            <SectionHeader>
              <SectionTitle>
                <FontAwesomeIcon icon={faComments} />
                Recent Feedback
              </SectionTitle>
              <SectionLink to="/feedback">
                View All <FontAwesomeIcon icon={faChevronRight} />
              </SectionLink>
            </SectionHeader>

            <FeedbackList>
              {feedback.map(item => (
                <FeedbackItem key={item.id}>
                  <FeedbackHeader>
                    <FeedbackAvatar>
                      <FeedbackImage src={item.avatar} alt={item.name} />
                    </FeedbackAvatar>
                    <FeedbackUser>
                      <FeedbackName>{item.name}</FeedbackName>
                      <FeedbackDate>{item.date}</FeedbackDate>
                    </FeedbackUser>
                    <FeedbackRating>
                      {[...Array(5)].map((_, i) => (
                        <FontAwesomeIcon 
                          key={i} 
                          icon={faStar} 
                          style={{ opacity: i < item.rating ? 1 : 0.3 }}
                        />
                      ))}
                    </FeedbackRating>
                  </FeedbackHeader>
                  <FeedbackContent>
                    {item.content}
                  </FeedbackContent>
                </FeedbackItem>
              ))}
            </FeedbackList>
          </DashboardSection>
        </div>

        <div>
          <DashboardSection>
            <SectionHeader>
              <SectionTitle>
                <FontAwesomeIcon icon={faUsers} />
                Connections
              </SectionTitle>
              <SectionLink to="/connections">
                View All <FontAwesomeIcon icon={faChevronRight} />
              </SectionLink>
            </SectionHeader>

            <ConnectionsList>
              {connections.map(connection => (
                <ConnectionItem key={connection.id}>
                  <ConnectionAvatar>
                    <ConnectionImage src={connection.avatar} alt={connection.name} />
                  </ConnectionAvatar>
                  <ConnectionInfo>
                    <ConnectionName>{connection.name}</ConnectionName>
                    <ConnectionSkill>{connection.skill}</ConnectionSkill>
                  </ConnectionInfo>
                </ConnectionItem>
              ))}
            </ConnectionsList>
          </DashboardSection>

          <DashboardSection>
            <SectionHeader>
              <SectionTitle>
                <FontAwesomeIcon icon={faChalkboardTeacher} />
                Skills I Teach
              </SectionTitle>
              <SectionLink to="/profile">
                Manage <FontAwesomeIcon icon={faChevronRight} />
              </SectionLink>
            </SectionHeader>

            <SkillsList>
              {currentUser?.skillsToTeach?.map((skill, index) => (
                <SkillTag key={index}>{skill}</SkillTag>
              )) || (
                <>
                  <SkillTag>React.js</SkillTag>
                  <SkillTag>JavaScript</SkillTag>
                  <SkillTag>Node.js</SkillTag>
                  <SkillTag>MongoDB</SkillTag>
                  <SkillTag>Express</SkillTag>
                </>
              )}
            </SkillsList>
          </DashboardSection>

          <DashboardSection>
            <SectionHeader>
              <SectionTitle>
                <FontAwesomeIcon icon={faGraduationCap} />
                Skills I'm Learning
              </SectionTitle>
              <SectionLink to="/profile">
                Manage <FontAwesomeIcon icon={faChevronRight} />
              </SectionLink>
            </SectionHeader>

            <SkillsList>
              {currentUser?.skillsToLearn?.map((skill, index) => (
                <SkillTag key={index}>{skill}</SkillTag>
              )) || (
                <>
                  <SkillTag>UI Design</SkillTag>
                  <SkillTag>UX Research</SkillTag>
                  <SkillTag>Python</SkillTag>
                </>
              )}
            </SkillsList>
          </DashboardSection>
        </div>
      </DashboardGrid>
    </DashboardContainer>
  );
};

export default Dashboard;
