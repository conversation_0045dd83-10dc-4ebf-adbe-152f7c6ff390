import { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faStar,
  faFilter,
  faGraduationCap,
  faCode,
  faPalette,
  faChartLine,
  faMusic,
  faLanguage,
  faHeartbeat,
  faUtensils,
  faUser,
  faMapMarkerAlt
} from '@fortawesome/free-solid-svg-icons';

import Section from '../../components/ui/Section';
import Button from '../../components/ui/Button';

// Import placeholder images
import images from '../../assets/placeholderImages';

// Styled Components
const DiscoverContainer = styled.div`
  display: flex;
  min-height: calc(100vh - 80px);
`;

const Sidebar = styled.div`
  width: 250px;
  background-color: #ffffff;
  color: var(--text-color);
  padding: 2rem 1rem;
  flex-shrink: 0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
`;

const SidebarSection = styled.div`
  margin-bottom: 2rem;
`;

const SidebarTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
`;

const SidebarItem = styled.div`
  padding: 0.75rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 0.5rem;

  &:hover, &.active {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &.active {
    color: var(--primary-color);
    font-weight: 500;
  }
`;

const CategoryItem = styled(SidebarItem)`
  display: flex;
  align-items: center;

  svg {
    margin-right: 0.75rem;
    width: 20px;
  }
`;

const MainContent = styled.div`
  flex: 1;
  background-color: #f8f9fa;
  padding: 2rem;
  overflow-y: auto;
`;

const SearchBar = styled.div`
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 50px;
  padding: 0.5rem 1.5rem;
  margin-bottom: 3rem;
  width: 100%;
  max-width: 600px;
  margin: 0 auto 3rem;

  svg {
    color: #757575;
    margin-right: 0.75rem;
  }

  input {
    flex: 1;
    border: none;
    padding: 0.5rem 0;
    font-size: 1rem;

    &:focus {
      outline: none;
    }
  }
`;

const SectionTitle = styled.h2`
  font-size: 2rem;
  color: var(--dark-color);
  margin-bottom: 2rem;
  margin-top: 2rem;
`;

const ProfilesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
`;

const ProfileCard = styled.div`
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--border-color);
  height: 100%;
  padding-bottom: 1.5rem;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const ProfileHeader = styled.div`
  padding: 2rem 1.5rem 1.5rem;
  text-align: center;
  position: relative;
  background: linear-gradient(to bottom, rgba(var(--primary-rgb), 0.05), rgba(255, 255, 255, 0));
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const ProfileAvatar = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 1.25rem;
  overflow: hidden;
  border: 4px solid var(--primary-color);
  box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.2);
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

const ProfileImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ProfileName = styled.h3`
  font-size: 1.6rem;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
  font-weight: 600;
`;

const ProfileRating = styled.div`
  display: flex;
  justify-content: center;
  color: #ffb400;
  margin-bottom: 1rem;
  filter: drop-shadow(0 2px 3px rgba(255, 180, 0, 0.2));

  svg {
    margin: 0 2px;
    font-size: 1.1rem;
  }
`;

const ProfileDescription = styled.p`
  color: var(--text-color);
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
  text-align: center;
  line-height: 1.5;
  padding: 0 0.5rem;
`;

const ProfileActions = styled.div`
  display: flex;
  justify-content: center;
  padding: 1rem 1.5rem;
  gap: 1rem;
  margin-top: auto;
`;

const SkillsSection = styled.div`
  background-color: #f8f9fa;
  padding: 1.25rem 1.5rem;
  border-top: 1px solid var(--border-color);
`;

const SkillsTitle = styled.h4`
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1rem;
  text-transform: uppercase;
  font-weight: 600;
  display: flex;
  align-items: center;

  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
    margin-right: 0.5rem;
  }
`;

const SkillsGrid = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

const SkillTag = styled.span`
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 50px;
  font-size: 0.85rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
`;

// Sample data
const categories = [
  { id: 'for-you', name: 'For You', icon: faUser },
  { id: 'popular', name: 'Popular', icon: faStar },
  { id: 'programming', name: 'Web Development', icon: faCode },
  { id: 'machine-learning', name: 'Data Science', icon: faChartLine },
  { id: 'design', name: 'Graphic Design', icon: faPalette },
  { id: 'soft-skills', name: 'Soft Skills', icon: faHeartbeat },
  { id: 'urdu', name: 'Urdu Literature', icon: faLanguage },
  { id: 'others', name: 'Others', icon: faFilter }
];

const profiles = [
  {
    id: 'ayesha12',
    name: 'Ayesha Malik',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    rating: 5,
    description: 'Computer Science student at NUST Islamabad specialising in data science and machine learning',
    skillsToTeach: ['Machine Learning', 'Python', 'Data Science', 'SQL'],
    skillsToLearn: ['UI Design', 'JavaScript', 'React', 'Node.js'],
    location: 'Islamabad'
  },
  {
    id: 'ahmad42',
    name: 'Ahmad Khan',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    rating: 4,
    description: 'Professor - Mathematics at LUMS Lahore. Specialising in Algebra and Statistics',
    skillsToTeach: ['Mathematics', 'Algebra', 'Arithmetic', 'Statistics'],
    skillsToLearn: ['Python', 'Data Science', 'Machine Learning'],
    location: 'Lahore'
  },
  {
    id: 'ali75',
    name: 'Ali Hassan',
    avatar: 'https://randomuser.me/api/portraits/men/68.jpg',
    rating: 5,
    description: 'Full-stack developer at Systems Ltd Karachi with 5 years of experience in React and Node.js',
    skillsToTeach: ['JavaScript', 'React', 'Node.js', 'MongoDB', 'Express'],
    skillsToLearn: ['UI Design', 'UX Research', 'Python', 'Machine Learning'],
    location: 'Karachi'
  },
  {
    id: 'ayesha12',
    name: 'Ayesha Malik',
    avatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    rating: 4,
    description: 'UX/UI designer at Techlogix Islamabad specializing in mobile app interfaces and user research',
    skillsToTeach: ['UI Design', 'Figma', 'User Research', 'Prototyping', 'Adobe XD'],
    skillsToLearn: ['JavaScript', 'React', 'HTML/CSS', 'Frontend Development'],
    location: 'Islamabad'
  },
  {
    id: 'usman55',
    name: 'Usman Farooq',
    avatar: 'https://randomuser.me/api/portraits/men/42.jpg',
    rating: 5,
    description: 'Cybersecurity expert at COMSATS University Islamabad with expertise in network security',
    skillsToTeach: ['Cybersecurity', 'Network Security', 'Ethical Hacking', 'Penetration Testing'],
    skillsToLearn: ['Cloud Security', 'DevSecOps', 'Blockchain Security'],
    location: 'Islamabad'
  },
  {
    id: 'zainab77',
    name: 'Zainab Qureshi',
    avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
    rating: 4,
    description: 'Literature professor at University of Karachi specializing in Urdu poetry and classical literature',
    skillsToTeach: ['Urdu Literature', 'Poetry', 'Creative Writing', 'Literary Analysis'],
    skillsToLearn: ['Digital Content Creation', 'Online Teaching Methods', 'Educational Technology'],
    location: 'Karachi'
  }
];

const Discover = () => {
  const [activeCategory, setActiveCategory] = useState('for-you');
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <DiscoverContainer>
      {/* Sidebar */}
      <Sidebar>
        <SidebarSection>
          <SidebarTitle>Categories</SidebarTitle>
          {categories.map(category => (
            <CategoryItem
              key={category.id}
              className={activeCategory === category.id ? 'active' : ''}
              onClick={() => setActiveCategory(category.id)}
            >
              <FontAwesomeIcon icon={category.icon} />
              {category.name}
            </CategoryItem>
          ))}
        </SidebarSection>
      </Sidebar>

      {/* Main Content */}
      <MainContent>
        {/* Search Bar */}
        <SearchBar>
          <FontAwesomeIcon icon={faSearch} />
          <input
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </SearchBar>

        {/* For You Section */}
        <SectionTitle>For You</SectionTitle>
        <ProfilesGrid>
          {profiles.map(profile => (
            <ProfileCard key={profile.id}>
              <ProfileHeader>
                <ProfileAvatar>
                  <ProfileImage src={profile.avatar} alt={profile.name} />
                </ProfileAvatar>
                <ProfileName>{profile.name}</ProfileName>
                <ProfileRating>
                  {[...Array(5)].map((_, i) => (
                    <FontAwesomeIcon
                      key={i}
                      icon={faStar}
                      style={{ opacity: i < profile.rating ? 1 : 0.3 }}
                    />
                  ))}
                </ProfileRating>
                <ProfileDescription>
                  {profile.description}
                </ProfileDescription>
                <div style={{ color: 'var(--text-light)', fontSize: '0.85rem', marginBottom: '1rem' }}>
                  <FontAwesomeIcon icon={faMapMarkerAlt} style={{ marginRight: '0.5rem' }} />
                  {profile.location}
                </div>

                <ProfileActions>
                  <Button
                    style={{
                      backgroundColor: '#f1f1f1',
                      color: 'var(--dark-color)',
                      border: '1px solid #e0e0e0',
                      padding: '0.6rem 0.5rem',
                      borderRadius: '50px',
                      fontWeight: 'bold',
                      transition: 'all 0.3s ease',
                      width: '120px'
                    }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.backgroundColor = '#e0e0e0';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.backgroundColor = '#f1f1f1';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                  >
                    Connect
                  </Button>
                  <Link to={`/profile/${profile.id}`} style={{ textDecoration: 'none' }}>
                    <Button
                      style={{
                        backgroundColor: '#f1f1f1',
                        color: 'var(--dark-color)',
                        border: '1px solid #e0e0e0',
                        padding: '0.6rem 0.5rem',
                        borderRadius: '50px',
                        transition: 'all 0.3s ease',
                        width: '120px'
                      }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.backgroundColor = '#e0e0e0';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.backgroundColor = '#f1f1f1';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}
                    >
                      View Profile
                    </Button>
                  </Link>
                </ProfileActions>
              </ProfileHeader>
            </ProfileCard>
          ))}
        </ProfilesGrid>
      </MainContent>
    </DiscoverContainer>
  );
};

export default Discover;
