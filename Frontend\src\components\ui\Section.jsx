const Section = ({
  title,
  subtitle,
  children,
  bgColor,
  bgImage,
  overlayOpacity = 0.5,
  spacing = "default",
  narrow = false,
  headerAlignment = "center",
  titleColor,
  subtitleColor,
  className = "",
}) => {
  // Spacing classes
  const spacingClasses = {
    sm: "py-xl",
    default: "py-xl",
    lg: "py-3xl",
  };

  // Header alignment classes
  const alignmentClasses = {
    left: "text-left",
    center: "text-center",
    right: "text-right",
  };

  // Subtitle margin classes based on alignment
  const subtitleMarginClasses = {
    left: "ml-0",
    center: "mx-auto",
    right: "mr-0",
  };

  // Base section classes
  const sectionClasses = `
    min-h-screen flex flex-col justify-center
    ${spacingClasses[spacing]}
    relative
    shadow-sm border-t border-b border-black/5
    z-10 mb-24
    ${className}
  `;

  return (
    <section className={sectionClasses} style={{ backgroundColor: bgColor }}>
      {/* Background image with overlay */}
      {bgImage && (
        <>
          <div
            className="absolute inset-0 bg-cover bg-center"
            style={{ backgroundImage: `url(${bgImage})` }}
          />
          <div
            className="absolute inset-0 z-10"
            style={{ backgroundColor: `rgba(0, 0, 0, ${overlayOpacity})` }}
          />
        </>
      )}

      {/* Content */}
      <div
        className={`
        w-full px-12 flex-1 flex flex-col justify-center
        ${bgImage ? "relative z-20" : ""}
        ${narrow ? "max-w-full" : ""}
      `}
      >
        {/* Header */}
        {(title || subtitle) && (
          <div className={`mb-2xl ${alignmentClasses[headerAlignment]}`}>
            {title && (
              <h2
                className="text-6xl lg:text-5xl md:text-4xl sm:text-3xl mb-md font-bold"
                style={{ color: titleColor }}
              >
                {title}
              </h2>
            )}
            {subtitle && (
              <p
                className={`
                  text-2xl lg:text-xl md:text-md sm:text-sm
                  max-w-2xl lg:max-w-xl md:max-w-lg sm:max-w-full
                  ${subtitleMarginClasses[headerAlignment]}
                `}
                style={{ color: subtitleColor }}
              >
                {subtitle}
              </p>
            )}
          </div>
        )}

        {/* Children content */}
        {children}
      </div>
    </section>
  );
};

export default Section;
