const Hero = ({
  title,
  subtitle,
  bgImage,
  children,
  alignment = "center",
  height = "100vh",
  overlayOpacity = 0.5,
  actions,
  className = "",
}) => {
  // Alignment classes
  const alignmentClasses = {
    left: "text-left",
    center: "text-center",
    right: "text-right",
  };

  // Subtitle alignment classes for centering/right-aligning content
  const subtitleAlignmentClasses = {
    center: "mx-auto",
    right: "ml-auto",
    left: "",
  };

  // Actions alignment classes
  const actionsAlignmentClasses = {
    center: "justify-center",
    right: "justify-end",
    left: "justify-start",
  };

  // Actions responsive alignment classes for mobile
  const actionsResponsiveClasses = {
    center: "sm:mx-auto",
    right: "sm:ml-auto",
    left: "",
  };

  return (
    <div
      className={`relative flex items-center bg-cover bg-center ${className}`}
      style={{
        backgroundImage: bgImage ? `url(${bgImage})` : undefined,
        height: height,
      }}
    >
      {/* Overlay */}
      <div
        className="absolute inset-0 z-10"
        style={{ backgroundColor: `rgba(0, 0, 0, ${overlayOpacity})` }}
      />

      {/* Content */}
      <div
        className={`relative z-20 w-full px-12 text-light ${alignmentClasses[alignment]}`}
      >
        {title && (
          <h1 className="text-6xl md:text-3xl font-extrabold mb-lg">
            {typeof title === "string" ? (
              <span dangerouslySetInnerHTML={{ __html: title }} />
            ) : (
              title
            )}
          </h1>
        )}

        {subtitle && (
          <p
            className={`text-2xl md:text-lg mb-xl max-w-2xl ${subtitleAlignmentClasses[alignment]}`}
          >
            {subtitle}
          </p>
        )}

        {actions && (
          <div
            className={`
            flex gap-md
            ${actionsAlignmentClasses[alignment]}
            max-sm:flex-col max-sm:w-full max-sm:max-w-xs
            ${actionsResponsiveClasses[alignment]}
          `}
          >
            {actions}
          </div>
        )}

        {children}
      </div>
    </div>
  );
};

export default Hero;
