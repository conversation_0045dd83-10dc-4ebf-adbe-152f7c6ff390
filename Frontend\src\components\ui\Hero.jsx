import styled from 'styled-components';

const HeroContainer = styled.div`
  position: relative;
  height: 100vh; // Always take full viewport height
  background-image: url(${props => props.bgImage});
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, ${props => props.overlayOpacity || 0.5});
    z-index: 1;
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 2;
  width: calc(100% - 6rem);
  margin: 0 3rem;
  color: var(--light-color);
  text-align: ${props => props.alignment || 'center'};

  ${props => props.alignment === 'left' && `
    text-align: left;
  `}

  ${props => props.alignment === 'right' && `
    text-align: right;
  `}
`;

const HeroTitle = styled.h1`
  font-size: 3.8rem;
  font-weight: 800;
  margin-bottom: var(--spacing-lg);

  span {
    color: var(--primary-color);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-3xl);
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.5rem;
  margin-bottom: var(--spacing-xl);
  max-width: 700px;

  ${props => props.alignment === 'center' && `
    margin-left: auto;
    margin-right: auto;
  `}

  ${props => props.alignment === 'right' && `
    margin-left: auto;
  `}

  @media (max-width: 768px) {
    font-size: var(--font-size-lg);
  }
`;

const HeroActions = styled.div`
  display: flex;
  gap: var(--spacing-md);

  ${props => props.alignment === 'center' && `
    justify-content: center;
  `}

  ${props => props.alignment === 'right' && `
    justify-content: flex-end;
  `}

  @media (max-width: 576px) {
    flex-direction: column;
    width: 100%;
    max-width: 300px;

    ${props => props.alignment === 'center' && `
      margin-left: auto;
      margin-right: auto;
    `}

    ${props => props.alignment === 'right' && `
      margin-left: auto;
    `}
  }
`;

const Hero = ({
  title,
  subtitle,
  bgImage,
  children,
  alignment = 'center',
  height,
  overlayOpacity,
  actions
}) => {
  return (
    <HeroContainer bgImage={bgImage} height={height} overlayOpacity={overlayOpacity}>
      <HeroContent alignment={alignment}>
        {title && <HeroTitle>{title}</HeroTitle>}
        {subtitle && <HeroSubtitle alignment={alignment}>{subtitle}</HeroSubtitle>}
        {actions && <HeroActions alignment={alignment}>{actions}</HeroActions>}
        {children}
      </HeroContent>
    </HeroContainer>
  );
};

export default Hero;
