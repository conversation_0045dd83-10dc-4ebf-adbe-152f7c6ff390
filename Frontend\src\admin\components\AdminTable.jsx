import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSort, faSortUp, faSortDown } from '@fortawesome/free-solid-svg-icons';

/**
 * AdminTable - Reusable table component for admin pages
 * 
 * @param {Object} props
 * @param {Array} props.columns - Table column definitions
 * @param {Array} props.data - Table data
 * @param {Object} props.sorting - Sorting state { field, direction }
 * @param {Function} props.onSort - Sort handler
 * @param {string} props.className - Additional CSS classes
 */
const AdminTable = ({ 
  columns = [], 
  data = [], 
  sorting = {}, 
  onSort, 
  className = '',
  ...props 
}) => {
  const getSortIcon = (field) => {
    if (sorting.field !== field) {
      return <FontAwesomeIcon icon={faSort} className="text-gray-400" />;
    }
    return sorting.direction === 'asc' 
      ? <FontAwesomeIcon icon={faSortUp} className="text-blue-600" />
      : <FontAwesomeIcon icon={faSortDown} className="text-blue-600" />;
  };

  const handleSort = (field) => {
    if (onSort) {
      onSort(field);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${className}`} {...props}>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={column.key || index}
                  onClick={() => column.sortable && handleSort(column.field)}
                  className={`
                    px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider
                    ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''}
                    ${column.className || ''}
                  `}
                >
                  <div className="flex items-center gap-2">
                    {column.title}
                    {column.sortable && getSortIcon(column.field)}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.length === 0 ? (
              <tr>
                <td 
                  colSpan={columns.length} 
                  className="px-6 py-12 text-center text-gray-500"
                >
                  No data available
                </td>
              </tr>
            ) : (
              data.map((row, rowIndex) => (
                <tr key={row.id || rowIndex} className="hover:bg-gray-50">
                  {columns.map((column, colIndex) => (
                    <td 
                      key={`${rowIndex}-${colIndex}`}
                      className={`px-6 py-4 whitespace-nowrap ${column.cellClassName || ''}`}
                    >
                      {column.render 
                        ? column.render(row[column.field], row, rowIndex)
                        : row[column.field]
                      }
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AdminTable;
