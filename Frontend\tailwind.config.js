/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom SkillSwap color scheme
        primary: {
          DEFAULT: '#7e57c2',  // Main purple
          dark: '#5e35b1',     // Darker purple for hover effects
          rgb: '126, 87, 194', // RGB values for alpha usage
        },
        secondary: {
          DEFAULT: '#9575cd',  // Lighter purple
          dark: '#7e57c2',     // Darker secondary for hover effects
          rgb: '149, 117, 205', // RGB values for alpha usage
        },
        accent: '#e83d6a',     // Pink accent color
        background: '#f8f9fa', // Light gray background
        dark: '#2d3748',       // Dark text/elements
        light: '#ffffff',      // White
        text: {
          DEFAULT: '#333333',  // Main text color
          light: '#666666',    // Light text color
        },
        border: '#e2e8f0',     // Border color
        success: '#48bb78',    // Green for success states
        warning: '#ed8936',    // Orange for warning states
        error: '#e53e3e',      // Red for error states

        // Keep some Tailwind defaults for flexibility
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        }
      },
      fontSize: {
        'xs': '0.75rem',     // 12px
        'sm': '0.875rem',    // 14px
        'md': '1rem',        // 16px (base)
        'lg': '1.125rem',    // 18px
        'xl': '1.25rem',     // 20px
        '2xl': '1.5rem',     // 24px
        '3xl': '1.875rem',   // 30px
        '4xl': '2.25rem',    // 36px
        '5xl': '3rem',       // 48px
        '6xl': '5rem',       // 80px
      },
      spacing: {
        'xs': '0.25rem',     // 4px
        'sm': '0.5rem',      // 8px
        'md': '1rem',        // 16px
        'lg': '1.5rem',      // 24px
        'xl': '2rem',        // 32px
        '2xl': '3rem',       // 48px
        '3xl': '4rem',       // 64px
      },
      borderRadius: {
        'sm': '0.25rem',     // 4px
        'md': '0.5rem',      // 8px
        'lg': '1rem',        // 16px
        'full': '9999px',    // Fully rounded
      },
      fontFamily: {
        sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', 'sans-serif'],
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'card': '0 4px 6px rgba(0, 0, 0, 0.05)',
        'card-hover': '0 10px 20px rgba(0, 0, 0, 0.1)',
        'button': '0 4px 8px rgba(0, 0, 0, 0.1)',
      },
      maxWidth: {
        'container': '1200px',
      },
      lineHeight: {
        'tight': '1.2',
        'normal': '1.6',
      },
      transitionProperty: {
        'all': 'all',
      },
      transitionDuration: {
        '300': '300ms',
      },
      transitionTimingFunction: {
        'ease': 'ease',
      },
    },
  },
  plugins: [],
}
