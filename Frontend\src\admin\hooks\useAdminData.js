import { useState, useEffect } from 'react';

/**
 * Custom hook for managing admin data with sorting, filtering, and pagination
 * 
 * @param {Array} initialData - Initial data array
 * @param {Object} options - Configuration options
 * @returns {Object} - Data management state and functions
 */
export const useAdminData = (initialData = [], options = {}) => {
  const {
    defaultSortField = 'id',
    defaultSortDirection = 'asc',
    itemsPerPage = 10,
    searchFields = []
  } = options;

  const [data, setData] = useState(initialData);
  const [filteredData, setFilteredData] = useState(initialData);
  const [sortField, setSortField] = useState(defaultSortField);
  const [sortDirection, setSortDirection] = useState(defaultSortDirection);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);

  // Update filtered data when search query or data changes
  useEffect(() => {
    let filtered = [...data];

    // Apply search filter
    if (searchQuery && searchFields.length > 0) {
      filtered = filtered.filter(item =>
        searchFields.some(field =>
          String(item[field]).toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredData(filtered);
    setCurrentPage(1); // Reset to first page when filtering
  }, [data, searchQuery, sortField, sortDirection, searchFields]);

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

  // Handlers
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const handlePageChange = (page) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const updateData = (newData) => {
    setData(newData);
  };

  const addItem = (item) => {
    setData(prev => [...prev, { ...item, id: Date.now() }]);
  };

  const updateItem = (id, updates) => {
    setData(prev => prev.map(item => 
      item.id === id ? { ...item, ...updates } : item
    ));
  };

  const removeItem = (id) => {
    setData(prev => prev.filter(item => item.id !== id));
  };

  return {
    // Data
    data: paginatedData,
    allData: filteredData,
    originalData: data,
    
    // State
    loading,
    searchQuery,
    sortField,
    sortDirection,
    currentPage,
    totalPages,
    totalItems: filteredData.length,
    
    // Handlers
    handleSort,
    handleSearch,
    handlePageChange,
    updateData,
    addItem,
    updateItem,
    removeItem,
    setLoading,
    
    // Pagination info
    pagination: {
      currentPage,
      totalPages,
      totalItems: filteredData.length,
      itemsPerPage,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }
  };
};

export default useAdminData;
