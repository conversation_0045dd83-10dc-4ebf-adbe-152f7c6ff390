import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFacebookF,
  faTwitter,
  faInstagram,
  faLinkedinIn
} from '@fortawesome/free-brands-svg-icons';
import {
  faEnvelope,
  faPhone,
  faMapMarkerAlt
} from '@fortawesome/free-solid-svg-icons';

const FooterContainer = styled.footer`
  background-color: var(--dark-color);
  color: var(--light-color);
  padding: var(--spacing-3xl) 0 var(--spacing-lg);
`;

const FooterContent = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const FooterColumn = styled.div`
  display: flex;
  flex-direction: column;

  p {
    font-size: 1rem;
    line-height: 1.6;
  }
`;

const FooterTitle = styled.h3`
  font-size: 2rem;
  margin-bottom: var(--spacing-lg);
  position: relative;

  &:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary-color);
  }
`;

const FooterLink = styled(Link)`
  color: var(--light-color);
  margin-bottom: var(--spacing-sm);
  transition: all 0.3s ease;
  font-size: 1rem;

  &:hover {
    color: var(--primary-color);
    transform: translateX(5px);
  }
`;

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
  font-size: 1rem;

  svg {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
  }
`;

const SocialLinks = styled.div`
  display: flex;
  margin-top: var(--spacing-md);
`;

const SocialLink = styled.a`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--light-color);
  margin-right: var(--spacing-sm);
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
  }
`;

const Copyright = styled.div`
  text-align: center;
  padding-top: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-lg);
  padding-right: var(--spacing-lg);
  font-size: 1.2rem;

  a {
    color: var(--primary-color);
  }
`;

const Footer = () => {
  return (
    <FooterContainer>
      <FooterContent>
        <FooterColumn>
          <FooterTitle>About SkillSwap</FooterTitle>
          <p>
            SkillSwap is a platform connecting skilled professionals with people who want to learn.
            We make skill sharing easy, accessible, and rewarding for everyone.
          </p>
          <SocialLinks>
            <SocialLink href="#" target="_blank" rel="noopener noreferrer">
              <FontAwesomeIcon icon={faFacebookF} />
            </SocialLink>
            <SocialLink href="#" target="_blank" rel="noopener noreferrer">
              <FontAwesomeIcon icon={faTwitter} />
            </SocialLink>
            <SocialLink href="#" target="_blank" rel="noopener noreferrer">
              <FontAwesomeIcon icon={faInstagram} />
            </SocialLink>
            <SocialLink href="#" target="_blank" rel="noopener noreferrer">
              <FontAwesomeIcon icon={faLinkedinIn} />
            </SocialLink>
          </SocialLinks>
        </FooterColumn>

        <FooterColumn>
          <FooterTitle>Quick Links</FooterTitle>
          <FooterLink to="/">Home</FooterLink>
          <FooterLink to="/about">About Us</FooterLink>
          <FooterLink to="/services">Services</FooterLink>
          <FooterLink to="/experts">Experts</FooterLink>
          <FooterLink to="/contact">Contact</FooterLink>
        </FooterColumn>

        <FooterColumn>
          <FooterTitle>Services</FooterTitle>
          <FooterLink to="/services">Skill Sharing</FooterLink>
          <FooterLink to="/services">Online Courses</FooterLink>
          <FooterLink to="/services">Mentorship</FooterLink>
          <FooterLink to="/services">Workshops</FooterLink>
          <FooterLink to="/services">Community Events</FooterLink>
        </FooterColumn>

        <FooterColumn>
          <FooterTitle>Contact Us</FooterTitle>
          <ContactItem>
            <FontAwesomeIcon icon={faMapMarkerAlt} />
            <span>Rashidi hostel MUET, Jamshoro</span>
          </ContactItem>
          <ContactItem>
            <FontAwesomeIcon icon={faPhone} />
            <span>03065805656</span>
          </ContactItem>
          <ContactItem>
            <FontAwesomeIcon icon={faEnvelope} />
            <span><EMAIL></span>
          </ContactItem>
        </FooterColumn>
      </FooterContent>

      <Copyright>
        <p>&copy; {new Date().getFullYear()} SkillSwap. All rights reserved.</p>
      </Copyright>
    </FooterContainer>
  );
};

export default Footer;
