import { Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFacebookF,
  faTwitter,
  faInstagram,
  faLinkedinIn,
} from "@fortawesome/free-brands-svg-icons";
import {
  faEnvelope,
  faPhone,
  faMapMarkerAlt,
} from "@fortawesome/free-solid-svg-icons";

const Footer = () => {
  return (
    <footer className="bg-dark text-light py-3xl">
      {/* Footer Content */}
      <div className="grid grid-cols-4 lg:grid-cols-2 sm:grid-cols-1 gap-xl max-w-container mx-auto px-lg">
        {/* About SkillSwap */}
        <div className="flex flex-col">
          <h3 className="text-2xl mb-lg relative after:content-[''] after:absolute after:-bottom-2 after:left-0 after:w-12 after:h-0.5 after:bg-primary">
            About SkillSwap
          </h3>
          <p className="text-md leading-normal">
            SkillSwap is a platform connecting skilled professionals with people
            who want to learn. We make skill sharing easy, accessible, and
            rewarding for everyone.
          </p>

          {/* Social Links */}
          <div className="flex mt-md">
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-10 h-10 bg-white/10 text-light rounded-full mr-sm transition-all duration-300 hover:bg-primary hover:-translate-y-1"
            >
              <FontAwesomeIcon icon={faFacebookF} />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-10 h-10 bg-white/10 text-light rounded-full mr-sm transition-all duration-300 hover:bg-primary hover:-translate-y-1"
            >
              <FontAwesomeIcon icon={faTwitter} />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-10 h-10 bg-white/10 text-light rounded-full mr-sm transition-all duration-300 hover:bg-primary hover:-translate-y-1"
            >
              <FontAwesomeIcon icon={faInstagram} />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-10 h-10 bg-white/10 text-light rounded-full mr-sm transition-all duration-300 hover:bg-primary hover:-translate-y-1"
            >
              <FontAwesomeIcon icon={faLinkedinIn} />
            </a>
          </div>
        </div>

        {/* Quick Links */}
        <div className="flex flex-col">
          <h3 className="text-2xl mb-lg relative after:content-[''] after:absolute after:-bottom-2 after:left-0 after:w-12 after:h-0.5 after:bg-primary">
            Quick Links
          </h3>
          <Link
            to="/"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Home
          </Link>
          <Link
            to="/about"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            About Us
          </Link>
          <Link
            to="/services"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Services
          </Link>
          <Link
            to="/experts"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Experts
          </Link>
          <Link
            to="/contact"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Contact
          </Link>
        </div>

        {/* Services */}
        <div className="flex flex-col">
          <h3 className="text-2xl mb-lg relative after:content-[''] after:absolute after:-bottom-2 after:left-0 after:w-12 after:h-0.5 after:bg-primary">
            Services
          </h3>
          <Link
            to="/services"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Skill Sharing
          </Link>
          <Link
            to="/services"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Online Courses
          </Link>
          <Link
            to="/services"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Mentorship
          </Link>
          <Link
            to="/services"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Workshops
          </Link>
          <Link
            to="/services"
            className="text-light mb-sm transition-all duration-300 text-md hover:text-primary hover:translate-x-1"
          >
            Community Events
          </Link>
        </div>

        {/* Contact Us */}
        <div className="flex flex-col">
          <h3 className="text-2xl mb-lg relative after:content-[''] after:absolute after:-bottom-2 after:left-0 after:w-12 after:h-0.5 after:bg-primary">
            Contact Us
          </h3>
          <div className="flex items-center mb-md text-md">
            <FontAwesomeIcon
              icon={faMapMarkerAlt}
              className="mr-sm text-primary"
            />
            <span>Rashidi hostel MUET, Jamshoro</span>
          </div>
          <div className="flex items-center mb-md text-md">
            <FontAwesomeIcon icon={faPhone} className="mr-sm text-primary" />
            <span>03065805656</span>
          </div>
          <div className="flex items-center mb-md text-md">
            <FontAwesomeIcon icon={faEnvelope} className="mr-sm text-primary" />
            <span><EMAIL></span>
          </div>
        </div>
      </div>

      {/* Copyright */}
      <div className="text-center pt-xl mt-xl border-t border-white/10 max-w-container mx-auto px-lg text-lg">
        <p>&copy; {new Date().getFullYear()} SkillSwap. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;
