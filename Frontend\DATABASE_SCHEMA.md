# SkillSwap Database Schema

This document outlines the database schema for the SkillSwap platform, focusing on the five key tables that form the core of the application.

## Key Database Tables

The SkillSwap platform is built around these five essential tables:

1. **Users** - Stores user profiles and login information
2. **Skills** - Defines all available skills on the platform
3. **User_Offered_Skills** - Tracks what skills users can teach
4. **User_Desired_Skills** - Tracks what skills users want to learn
5. **Matches** - Records successful skill pairings between users

## Table Structures and Relationships

### 1. Users

Stores user profiles and login information.

```
Table: users
```

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| username | VARCHAR(50) | UNIQUE, NOT NULL | User's username |
| email | VARCHAR(100) | UNIQUE, NOT NULL | User's email address |
| password | VARCHAR(255) | NOT NULL | Hashed password |
| name | VARCHAR(100) | NOT NULL | User's full name |
| avatar | VARCHAR(255) | | URL to profile image |
| bio | TEXT | | User's biography |
| current_status | VARCHAR(50) | | Employment/education status |
| state | VARCHAR(50) | | User's state/province |
| country | VARCHAR(50) | | User's country |
| education_level | VARCHAR(50) | | Highest education level |
| university_name | VARCHAR(100) | | Institution name |
| status | ENUM | NOT NULL, DEFAULT 'pending' | 'active', 'pending', 'suspended', 'blocked' |
| is_admin | BOOLEAN | NOT NULL, DEFAULT false | Admin privileges flag |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Account creation time |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

### 2. Skills

Defines all available skills on the platform.

```
Table: skills
```

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| name | VARCHAR(100) | UNIQUE, NOT NULL | Skill name |
| category | VARCHAR(50) | NOT NULL | Skill category (e.g., Tech, Business, Creative) |
| description | TEXT | | Skill description |
| requires_certification | BOOLEAN | NOT NULL, DEFAULT false | Whether certification is required |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

### 3. User_Offered_Skills

Tracks what skills users can teach.

```
Table: user_offered_skills
```

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| user_id | INT | FOREIGN KEY (users.id), NOT NULL | User reference |
| skill_id | INT | FOREIGN KEY (skills.id), NOT NULL | Skill reference |
| proficiency_level | ENUM | NOT NULL | 'intermediate', 'advanced', 'expert' |
| years_experience | INT | | Years of experience with the skill |
| description | TEXT | | User's description of their expertise |
| has_certification | BOOLEAN | NOT NULL, DEFAULT false | Whether user has certification |
| certification_file | VARCHAR(255) | | Path to certification file if uploaded |
| status | ENUM | NOT NULL, DEFAULT 'pending' | 'approved', 'pending', 'rejected' |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

### 4. User_Desired_Skills

Tracks what skills users want to learn.

```
Table: user_desired_skills
```

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| user_id | INT | FOREIGN KEY (users.id), NOT NULL | User reference |
| skill_id | INT | FOREIGN KEY (skills.id), NOT NULL | Skill reference |
| interest_level | ENUM | NOT NULL | 'low', 'medium', 'high' |
| current_knowledge | ENUM | | 'none', 'beginner', 'some' |
| learning_goal | TEXT | | User's goal for learning this skill |
| status | ENUM | NOT NULL, DEFAULT 'active' | 'active', 'inactive' |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

### 5. Matches

Records successful skill pairings between users.

```
Table: matches
```

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| teacher_skill_id | INT | FOREIGN KEY (user_offered_skills.id), NOT NULL | Reference to the offered skill |
| learner_skill_id | INT | FOREIGN KEY (user_desired_skills.id), NOT NULL | Reference to the desired skill |
| status | ENUM | NOT NULL, DEFAULT 'pending' | 'pending', 'accepted', 'rejected', 'completed' |
| teacher_id | INT | FOREIGN KEY (users.id), NOT NULL | User who will teach |
| learner_id | INT | FOREIGN KEY (users.id), NOT NULL | User who will learn |
| scheduled_date | DATETIME | | When the skill exchange is scheduled |
| duration_minutes | INT | | Planned duration of the exchange |
| notes | TEXT | | Additional notes about the match |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Match creation time |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE | Last update time |

## Database Relationships and Connectivity

The five tables described above form the core of the SkillSwap platform's database schema. Here's how they connect to each other:

### Key Relationships

1. **Users to User_Offered_Skills**: One-to-many
   - A user can offer to teach multiple skills
   - Each offered skill belongs to exactly one user

2. **Users to User_Desired_Skills**: One-to-many
   - A user can want to learn multiple skills
   - Each desired skill belongs to exactly one user

3. **Skills to User_Offered_Skills**: One-to-many
   - A skill can be offered by multiple users
   - Each offered skill entry references exactly one skill from the skills table

4. **Skills to User_Desired_Skills**: One-to-many
   - A skill can be desired by multiple users
   - Each desired skill entry references exactly one skill from the skills table

5. **User_Offered_Skills to Matches**: One-to-many
   - An offered skill can be part of multiple matches (a teacher can teach multiple learners)
   - Each match references exactly one offered skill

6. **User_Desired_Skills to Matches**: One-to-many
   - A desired skill can be part of multiple matches (a learner can learn from multiple teachers)
   - Each match references exactly one desired skill

### Database Diagram

```
+----------------+       +-------------------+       +----------------+
|                |       |                   |       |                |
|     Users      |<----->| User_Offered_Skills |<----->|    Skills     |
|                |       |                   |       |                |
+----------------+       +--------+----------+       +----------------+
        ^                         |                          ^
        |                         |                          |
        |                         v                          |
        |                +-----------------+                 |
        |                |                 |                 |
        |                |     Matches     |                 |
        |                |                 |                 |
        |                +--------+--------+                 |
        |                         |                          |
        |                         |                          |
        |                         v                          |
        |                +--------+----------+               |
        |                |                   |               |
        +--------------->| User_Desired_Skills |<--------------+
                         |                   |
                         +-------------------+
```

## Admin Functionality

The database schema supports the admin functionality requirements:

1. **User Management**
   - The `status` field in the Users table allows admins to approve, block, or suspend users
   - Admin privileges are controlled by the `is_admin` field

2. **Skill Listings Moderation**
   - The `status` field in User_Offered_Skills allows admins to approve, edit, or remove skill listings
   - The `has_certification` and `certification_file` fields support certification verification
   - Only skills with status='approved' would be visible to other users

## Conclusion

This database schema provides a solid foundation for the SkillSwap platform, focusing on the core functionality of matching users who can teach skills with users who want to learn those skills. The schema is designed to be:

1. **Efficient**: Separating offered and desired skills into different tables allows for more efficient queries
2. **Flexible**: The structure can accommodate various skill types and matching scenarios
3. **Scalable**: The schema can be extended with additional tables for messaging, notifications, etc.
4. **Secure**: Admin approval processes are built into the schema

As the platform grows, additional tables can be added for features like messaging, notifications, feedback, and more detailed user profiles.
