const Card = ({
  title,
  description,
  imageSrc,
  footer,
  children,
  hoverable = false,
  bordered = false,
  className = "",
  ...props
}) => {
  // Base card classes
  const baseClasses = `
    bg-light rounded-md overflow-hidden shadow-card transition-all duration-300 ease
  `;

  // Conditional classes
  const hoverClasses = hoverable
    ? "hover:-translate-y-1 hover:shadow-card-hover"
    : "";
  const borderClasses = bordered ? "border border-border" : "";

  // Combine all classes
  const cardClasses =
    `${baseClasses} ${hoverClasses} ${borderClasses} ${className}`.trim();

  return (
    <div className={cardClasses} {...props}>
      {imageSrc && (
        <div
          className="w-full h-48 bg-cover bg-center"
          style={{ backgroundImage: `url(${imageSrc})` }}
        />
      )}
      <div className="p-lg">
        {title && (
          <h3 className="text-xl mb-sm text-dark font-semibold">{title}</h3>
        )}
        {description && <p className="text-text-light mb-md">{description}</p>}
        {children}
      </div>
      {footer && (
        <div className="px-lg py-md border-t border-border flex justify-between items-center">
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;
