@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base layer customizations */
@layer base {
  * {
    @apply m-0 p-0 box-border;
  }

  body {
    @apply font-sans bg-background text-text leading-normal;
  }

  a {
    @apply no-underline text-primary transition-colors duration-300 ease;
  }

  a:hover {
    @apply text-accent;
  }

  button {
    @apply cursor-pointer font-sans;
  }

  img {
    @apply max-w-full h-auto;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold leading-tight mb-4;
  }

  p {
    @apply mb-4;
  }

  ul, ol {
    @apply mb-4 pl-6;
  }

  section {
    @apply py-3xl;
  }
}

/* Component layer for reusable patterns */
@layer components {
  .container {
    @apply w-full max-w-container mx-auto px-lg;
  }

  .Logo {
    @apply w-10 h-10 rounded-full;
  }
}