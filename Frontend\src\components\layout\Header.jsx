import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faBars,
  faTimes,
  faSignInAlt,
} from "@fortawesome/free-solid-svg-icons";
import UserDropdown from "../ui/UserDropdown";
import "../../App.css";

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { currentUser, logout } = useAuth();

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    closeMenu();
  }, [location]);

  return (
    <header
      className="bg-light shadow-lg sticky top-0 z-50 transition-all duration-300 w-full"
      style={{
        padding: scrolled ? "0.5rem 0" : "1rem 0",
      }}
    >
      <div className="flex justify-between items-center px-lg w-full mx-auto">
        {/* Logo */}
        <Link
          to="/"
          className="text-xl font-bold text-primary flex items-center"
        >
          <img className="Logo" src="images/logo.png" alt="" />
        </Link>

        {/* Mobile Menu Button */}
        <button
          onClick={toggleMenu}
          className="hidden max-md:block bg-transparent border-none text-dark text-xl cursor-pointer z-50"
        >
          <FontAwesomeIcon icon={isOpen ? faTimes : faBars} />
        </button>

        {/* Navigation Links */}
        <nav
          className={`
            flex items-center
            max-md:fixed max-md:top-0 max-md:w-3/5 max-md:h-screen
            max-md:bg-light max-md:flex-col max-md:justify-start max-md:pt-20
            max-md:transition-all max-md:duration-300 max-md:shadow-lg
            ${isOpen ? "max-md:right-0" : "max-md:-right-full"}
          `}
        >
          {/* Navigation Links */}
          <Link
            to="/home"
            className={`
              mx-4 text-dark font-medium relative text-lg transition-colors duration-300
              hover:text-primary
              max-md:my-4 max-md:text-xl
              ${location.pathname === "/home" ? "text-primary" : ""}
            `}
          >
            Home
            {location.pathname === "/home" && (
              <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-primary" />
            )}
          </Link>

          <Link
            to="/about"
            className={`
              mx-4 text-dark font-medium relative text-lg transition-colors duration-300
              hover:text-primary
              max-md:my-4 max-md:text-xl
              ${location.pathname === "/about" ? "text-primary" : ""}
            `}
          >
            About
            {location.pathname === "/about" && (
              <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-primary" />
            )}
          </Link>

          <Link
            to="/services"
            className={`
              mx-4 text-dark font-medium relative text-lg transition-colors duration-300
              hover:text-primary
              max-md:my-4 max-md:text-xl
              ${location.pathname === "/services" ? "text-primary" : ""}
            `}
          >
            Services
            {location.pathname === "/services" && (
              <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-primary" />
            )}
          </Link>

          <Link
            to="/discover"
            className={`
              mx-4 text-dark font-medium relative text-lg transition-colors duration-300
              hover:text-primary
              max-md:my-4 max-md:text-xl
              ${location.pathname === "/discover" ? "text-primary" : ""}
            `}
          >
            Discover
            {location.pathname === "/discover" && (
              <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-primary" />
            )}
          </Link>

          <Link
            to="/contact"
            className={`
              mx-4 text-dark font-medium relative text-lg transition-colors duration-300
              hover:text-primary
              max-md:my-4 max-md:text-xl
              ${location.pathname === "/contact" ? "text-primary" : ""}
            `}
          >
            Contact
            {location.pathname === "/contact" && (
              <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-primary" />
            )}
          </Link>

          {/* User Authentication */}
          {currentUser ? (
            <UserDropdown user={currentUser} onLogout={handleLogout} />
          ) : (
            <Link
              to="/login"
              className="flex items-center gap-2 bg-primary text-white px-4 py-2 rounded font-semibold text-lg transition-colors duration-300 ml-4 hover:bg-primary-dark max-md:my-4"
            >
              <FontAwesomeIcon icon={faSignInAlt} />
              Login
            </Link>
          )}
        </nav>
      </div>
    </header>
  );
};

export default Header;
