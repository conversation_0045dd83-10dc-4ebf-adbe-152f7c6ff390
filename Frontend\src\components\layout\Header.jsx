import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars, faTimes, faSignInAlt } from '@fortawesome/free-solid-svg-icons';
import UserDropdown from '../ui/UserDropdown';
import "../../App.css";

const HeaderContainer = styled.header`
  background-color: var(--light-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all 0.3s ease;
  width: 100%;
`;

const NavContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem var(--spacing-lg);
  width: 100%;
  margin: 0 auto;
`;

const Logo = styled(Link)`
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;

  span {
    color: var(--accent-color);
  }
`;

const NavLinks = styled.nav`
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    position: fixed;
    top: 0;
    right: ${({ isOpen }) => (isOpen ? '0' : '-100%')};
    width: 70%;
    height: 100vh;
    background-color: var(--light-color);
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 5rem;
    transition: right 0.3s ease-in-out;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  }
`;

const NavLink = styled(Link)`
  margin: 0 1rem;
  color: var(--dark-color);
  font-weight: 500;
  position: relative;
  font-size: 1.5rem;

  &:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
  }

  &:hover:after, &.active:after {
    width: 100%;
  }

  &.active {
    color: var(--primary-color);
  }

  @media (max-width: 768px) {
    margin: 1rem 0;
    font-size: 1.5rem;
  }
`;

const LoginButton = styled(Link)`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 1.5rem;
  transition: background-color 0.3s ease;
  margin-left: 1rem;

  &:hover {
    background-color: #5a3cc0;
  }

  @media (max-width: 768px) {
    margin: 1rem 0;
  }
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #dc3545;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  transition: background-color 0.3s ease;
  margin-left: 1rem;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: #c82333;
  }

  @media (max-width: 768px) {
    margin: 1rem 0;
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
  font-weight: 500;
  color: var(--primary-color);

  @media (max-width: 768px) {
    margin: 1rem 0;
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  color: var(--dark-color);
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 101;

  @media (max-width: 768px) {
    display: block;
  }
`;

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { currentUser, logout } = useAuth();

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    closeMenu();
  }, [location]);

  return (
    <HeaderContainer style={{
      padding: scrolled ? '0.5rem 0' : '1rem 0',
    }}>
      <NavContainer>
        <Logo to="/">
          <img className='Logo' src="images/logo.png" alt="" />
        </Logo>

        <MobileMenuButton onClick={toggleMenu}>
          <FontAwesomeIcon icon={isOpen ? faTimes : faBars} />
        </MobileMenuButton>

        <NavLinks isOpen={isOpen}>
          <NavLink
            to="/home"
            className={location.pathname === '/home' ? 'active' : ''}
          >
            Home
          </NavLink>
          <NavLink
            to="/about"
            className={location.pathname === '/about' ? 'active' : ''}
          >
            About
          </NavLink>
          <NavLink
            to="/services"
            className={location.pathname === '/services' ? 'active' : ''}
          >
            Services
          </NavLink>
          <NavLink
            to="/discover"
            className={location.pathname === '/discover' ? 'active' : ''}
          >
            Discover
          </NavLink>
          <NavLink
            to="/contact"
            className={location.pathname === '/contact' ? 'active' : ''}
          >
            Contact
          </NavLink>

          {currentUser ? (
            <UserDropdown user={currentUser} onLogout={handleLogout} />
          ) : (
            <LoginButton to="/login">
              <FontAwesomeIcon icon={faSignInAlt} />
              Login
            </LoginButton>
          )}
        </NavLinks>
      </NavContainer>
    </HeaderContainer>
  );
};

export default Header;
