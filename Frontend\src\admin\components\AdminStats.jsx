import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

/**
 * AdminStats - Statistics card component for admin dashboard
 * 
 * @param {Object} props
 * @param {string} props.title - Stat title
 * @param {string|number} props.value - Stat value
 * @param {string} props.change - Change percentage (optional)
 * @param {string} props.changeType - 'increase' | 'decrease' | 'neutral'
 * @param {Object} props.icon - FontAwesome icon
 * @param {string} props.iconColor - Icon background color
 * @param {string} props.className - Additional CSS classes
 */
const AdminStats = ({ 
  title, 
  value, 
  change, 
  changeType = 'neutral',
  icon, 
  iconColor = 'bg-blue-500',
  className = '',
  ...props 
}) => {
  const getChangeColor = () => {
    switch (changeType) {
      case 'increase':
        return 'text-green-600';
      case 'decrease':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getChangeIcon = () => {
    switch (changeType) {
      case 'increase':
        return '↗';
      case 'decrease':
        return '↘';
      default:
        return '→';
    }
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}
      {...props}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className={`text-sm ${getChangeColor()} flex items-center mt-1`}>
              <span className="mr-1">{getChangeIcon()}</span>
              {change}
            </p>
          )}
        </div>
        {icon && (
          <div className={`flex items-center justify-center w-12 h-12 ${iconColor} rounded-lg`}>
            <FontAwesomeIcon icon={icon} className="text-white text-lg" />
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminStats;
