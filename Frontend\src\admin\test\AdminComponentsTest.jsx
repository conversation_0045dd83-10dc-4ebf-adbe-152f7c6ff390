import React from 'react';
import { 
  AdminCard, 
  AdminTable, 
  AdminStats,
  useAdminData,
  formatAdminDate,
  getStatusBadgeClasses 
} from '../index';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers, faGraduationCap, faExchangeAlt } from '@fortawesome/free-solid-svg-icons';

/**
 * Test component to verify all admin components are working correctly
 * This component can be used for development testing and debugging
 */
const AdminComponentsTest = () => {
  // Test data
  const testUsers = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'active',
      joinDate: '2023-01-15'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'inactive',
      joinDate: '2023-02-20'
    }
  ];

  // Test useAdminData hook
  const {
    data,
    handleSort,
    handleSearch,
    sortField,
    sortDirection
  } = useAdminData(testUsers, {
    searchFields: ['name', 'email'],
    defaultSortField: 'name'
  });

  // Test table columns
  const columns = [
    {
      field: 'name',
      title: 'Name',
      sortable: true
    },
    {
      field: 'email',
      title: 'Email',
      sortable: true
    },
    {
      field: 'status',
      title: 'Status',
      render: (value) => (
        <span className={getStatusBadgeClasses(value)}>
          {value}
        </span>
      )
    },
    {
      field: 'joinDate',
      title: 'Join Date',
      render: (value) => formatAdminDate(value)
    }
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        Admin Components Test
      </h1>

      {/* Test AdminStats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <AdminStats
          title="Total Users"
          value="1,234"
          change="+12%"
          changeType="increase"
          icon={faUsers}
          iconColor="bg-blue-500"
        />
        <AdminStats
          title="Active Skills"
          value="567"
          change="+5%"
          changeType="increase"
          icon={faGraduationCap}
          iconColor="bg-green-500"
        />
        <AdminStats
          title="Exchanges"
          value="89"
          change="-2%"
          changeType="decrease"
          icon={faExchangeAlt}
          iconColor="bg-purple-500"
        />
      </div>

      {/* Test AdminCard */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <AdminCard
          title="User Management"
          subtitle="Manage user accounts and permissions"
          icon={faUsers}
        >
          <p className="text-gray-600">
            This card demonstrates the AdminCard component with an icon and content.
          </p>
          <button className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Manage Users
          </button>
        </AdminCard>

        <AdminCard
          title="System Status"
          subtitle="Current system health and metrics"
        >
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Server Status:</span>
              <span className="text-green-600 font-medium">Online</span>
            </div>
            <div className="flex justify-between">
              <span>Database:</span>
              <span className="text-green-600 font-medium">Connected</span>
            </div>
            <div className="flex justify-between">
              <span>Last Backup:</span>
              <span className="text-gray-600">2 hours ago</span>
            </div>
          </div>
        </AdminCard>
      </div>

      {/* Test AdminTable */}
      <AdminCard title="User Data Table" subtitle="Testing AdminTable component">
        <div className="mb-4">
          <input
            type="text"
            placeholder="Search users..."
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <AdminTable
          columns={columns}
          data={data}
          sorting={{ field: sortField, direction: sortDirection }}
          onSort={handleSort}
        />
      </AdminCard>

      {/* Test utility functions */}
      <AdminCard title="Utility Functions Test" className="mt-6">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Date Formatting:</h4>
            <p className="text-gray-600">
              Original: 2023-12-01 → Formatted: {formatAdminDate('2023-12-01')}
            </p>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Status Badges:</h4>
            <div className="flex gap-2 flex-wrap">
              {['active', 'inactive', 'pending', 'suspended'].map(status => (
                <span key={status} className={getStatusBadgeClasses(status)}>
                  {status}
                </span>
              ))}
            </div>
          </div>
        </div>
      </AdminCard>
    </div>
  );
};

export default AdminComponentsTest;
