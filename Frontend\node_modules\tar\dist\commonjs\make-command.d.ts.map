{"version": 3, "file": "make-command.d.ts", "sourceRoot": "", "sources": ["../../src/make-command.ts"], "names": [], "mappings": "AAAA,OAAO,EAML,UAAU,EACV,mBAAmB,EACnB,qBAAqB,EACrB,kBAAkB,EAClB,oBAAoB,EACpB,qBAAqB,EACrB,0BAA0B,EAC1B,8BAA8B,EAC9B,gCAAgC,EAChC,yBAAyB,EACzB,2BAA2B,EAC3B,yBAAyB,EACzB,6BAA6B,EAC7B,+BAA+B,EAChC,MAAM,cAAc,CAAA;AAErB,MAAM,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,GAAG,CAAA;AAEpC,MAAM,MAAM,UAAU,CACpB,UAAU,EACV,SAAS,SAAS;IAAE,IAAI,EAAE,IAAI,CAAA;CAAE,IAC9B;IAEF,IAAI,UAAU,CAAA;IACd,CAAC,GAAG,EAAE,gCAAgC,GAAG,UAAU,CAAA;IACnD,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,UAAU,CAAA;IAC/B,CACE,GAAG,EAAE,gCAAgC,EACrC,OAAO,EAAE,MAAM,EAAE,GAChB,UAAU,CAAA;CACd,GAAG;IAEF,CAAC,GAAG,EAAE,+BAA+B,GAAG,SAAS,CAAA;IACjD,CAAC,GAAG,EAAE,+BAA+B,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,SAAS,CAAA;CACrE,GAAG;IAEF,CAAC,GAAG,EAAE,8BAA8B,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACpD,CACE,GAAG,EAAE,8BAA8B,EACnC,OAAO,EAAE,MAAM,EAAE,GAChB,OAAO,CAAC,IAAI,CAAC,CAAA;IAChB,CAAC,GAAG,EAAE,8BAA8B,EAAE,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC5D,CACE,GAAG,EAAE,8BAA8B,EACnC,OAAO,EAAE,MAAM,EAAE,EACjB,EAAE,EAAE,EAAE,GACL,OAAO,CAAC,IAAI,CAAC,CAAA;CACjB,GAAG;IAEF,CAAC,GAAG,EAAE,6BAA6B,GAAG,IAAI,CAAA;IAC1C,CAAC,GAAG,EAAE,6BAA6B,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;CAC9D,GAAG;IAEF,CAAC,GAAG,EAAE,yBAAyB,GAAG,OAAO,GAAG,SAAS,CACnD,yBAAyB,CAC1B,GACC,IAAI,GACJ,OAAO,GAAG,SAAS,2BAA2B,GAAG,SAAS,GAC1D,IAAI,GAAG,SAAS,CAAA;IAClB,CACE,GAAG,EAAE,yBAAyB,EAC9B,OAAO,EAAE,MAAM,EAAE,GAChB,OAAO,GAAG,SAAS,yBAAyB,GAAG,IAAI,GACpD,OAAO,GAAG,SAAS,2BAA2B,GAAG,SAAS,GAC1D,IAAI,GAAG,SAAS,CAAA;CACnB,GAAG;IAEF,CAAC,GAAG,EAAE,0BAA0B,GAAG,OAAO,GAAG,SAAS,CACpD,yBAAyB,CAC1B,GACC,OAAO,CAAC,IAAI,CAAC,GACb,OAAO,GAAG,SAAS,2BAA2B,GAAG,UAAU,GAC3D,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAA;IAC5B,CACE,GAAG,EAAE,0BAA0B,EAC/B,OAAO,EAAE,MAAM,EAAE,GAChB,OAAO,GAAG,SAAS,yBAAyB,GAAG,OAAO,CAAC,IAAI,CAAC,GAC7D,OAAO,GAAG,SAAS,2BAA2B,GAAG,UAAU,GAC3D,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAA;IAC5B,CAAC,GAAG,EAAE,0BAA0B,EAAE,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACxD,CACE,GAAG,EAAE,0BAA0B,EAC/B,OAAO,EAAE,MAAM,EAAE,EACjB,EAAE,EAAE,EAAE,GACL,OAAO,GAAG,SAAS,yBAAyB,GAAG,OAAO,CAAC,IAAI,CAAC,GAC7D,OAAO,GAAG,SAAS,2BAA2B,GAAG,KAAK,GACtD,OAAO,CAAC,IAAI,CAAC,CAAA;CAChB,GAAG;IAEF,CAAC,GAAG,EAAE,yBAAyB,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;IACtD,CACE,GAAG,EAAE,yBAAyB,EAC9B,OAAO,EAAE,MAAM,EAAE,GAChB,OAAO,GAAG,SAAS,yBAAyB,GAAG,IAAI,GACpD,OAAO,GAAG,SAAS,0BAA0B,GAAG,OAAO,CAAC,IAAI,CAAC,GAC7D,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;IACtB,CAAC,GAAG,EAAE,yBAAyB,EAAE,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACvD,CACE,GAAG,EAAE,yBAAyB,EAC9B,OAAO,EAAE,MAAM,EAAE,EACjB,EAAE,EAAE,EAAE,GACL,OAAO,GAAG,SAAS,yBAAyB,GAAG,KAAK,GACrD,OAAO,GAAG,SAAS,0BAA0B,GAAG,OAAO,CAAC,IAAI,CAAC,GAC7D,OAAO,CAAC,IAAI,CAAC,CAAA;CAChB,GAAG;IAEF,CAAC,GAAG,EAAE,2BAA2B,GAAG,OAAO,GAAG,SAAS,CACrD,yBAAyB,CAC1B,GACC,SAAS,GACT,OAAO,GAAG,SAAS,0BAA0B,GAAG,UAAU,GAC1D,SAAS,GAAG,UAAU,CAAA;IACxB,CACE,GAAG,EAAE,2BAA2B,EAChC,OAAO,EAAE,MAAM,EAAE,GAChB,OAAO,GAAG,SAAS,yBAAyB,GAAG,SAAS,GACzD,OAAO,GAAG,SAAS,0BAA0B,GAAG,UAAU,GAC1D,SAAS,GAAG,UAAU,CAAA;CACzB,GAAG;IAEF,CAAC,GAAG,EAAE,qBAAqB,GAAG,OAAO,GAAG,SAAS,CAC/C,yBAAyB,CAC1B,GACC,OAAO,GAAG,SAAS,yBAAyB,GAAG,IAAI,GACjD,OAAO,GAAG,SAAS,0BAA0B,GAAG,OAAO,CAAC,IAAI,CAAC,GAC7D,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,GACtB,OAAO,GAAG,SAAS,2BAA2B,GAC9C,OAAO,GAAG,SAAS,yBAAyB,GAAG,SAAS,GACtD,OAAO,GAAG,SAAS,0BAA0B,GAAG,UAAU,GAC1D,SAAS,GAAG,UAAU,GACxB,OAAO,GAAG,SAAS,yBAAyB,GAAG,SAAS,GAAG,IAAI,GAC/D,OAAO,GAAG,SAAS,0BAA0B,GAC7C,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,GAC1B,SAAS,GAAG,IAAI,GAAG,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;CAChD,GAAG;IAEF,QAAQ,EAAE,CAAC,GAAG,EAAE,kBAAkB,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,IAAI,CAAA;IAC9D,SAAS,EAAE,CACT,GAAG,EAAE,mBAAmB,EACxB,OAAO,EAAE,MAAM,EAAE,EACjB,EAAE,CAAC,EAAE,EAAE,KACJ,OAAO,CAAC,IAAI,CAAC,CAAA;IAClB,UAAU,EAAE,CACV,GAAG,EAAE,oBAAoB,EACzB,OAAO,EAAE,MAAM,EAAE,KACd,SAAS,CAAA;IACd,WAAW,EAAE,CACX,GAAG,EAAE,qBAAqB,EAC1B,OAAO,EAAE,MAAM,EAAE,KACd,UAAU,CAAA;IACf,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,IAAI,CAAA;CACzD,CAAA;AAED,eAAO,MAAM,WAAW;UAEI,IAAI;aAEpB,CAAC,GAAG,EAAE,kBAAkB,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,IAAI,aACnD,CACT,GAAG,EAAE,mBAAmB,EACxB,OAAO,EAAE,MAAM,EAAE,EACjB,EAAE,CAAC,EAAE,EAAE,KACJ,QAAQ,IAAI,CAAC,cACN,CACV,GAAG,EAAE,oBAAoB,EACzB,OAAO,EAAE,MAAM,EAAE,KACd,SAAS,eACD,CACX,GAAG,EAAE,qBAAqB,EAC1B,OAAO,EAAE,MAAM,EAAE,KACd,UAAU,aACJ,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,IAAI,KACvD,WAAW,UAAU,EAAE,SAAS,CAmElC,CAAA"}