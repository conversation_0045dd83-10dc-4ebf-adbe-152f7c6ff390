import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle,
  faTimesCircle,
  faBan,
  faUndo,
  faFlag,
  faExclamationTriangle,
  faSearch,
  faFilter,
  faEye,
  faComment
} from '@fortawesome/free-solid-svg-icons';

// Styled Components
const Container = styled.div``;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  color: var(--dark-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FilterBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
`;

const SearchInput = styled.div`
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  flex: 1;
  max-width: 400px;
  border: 1px solid var(--border-color);

  input {
    border: none;
    background: transparent;
    padding: 0.5rem;
    width: 100%;
    outline: none;
    color: var(--dark-color);
  }

  .search-icon {
    color: var(--text-muted);
    margin-right: 0.5rem;
  }
`;

const FilterSelect = styled.select`
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: white;
  color: var(--dark-color);
  outline: none;
  cursor: pointer;

  &:focus {
    border-color: var(--primary-color);
  }
`;

const SkillsTable = styled.div`
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
  background-color: #f8f9fa;
  padding: 1rem;
  font-weight: 600;
  color: var(--dark-color);
  border-bottom: 1px solid var(--border-color);
`;

const TableRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  align-items: center;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const SkillInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const SkillName = styled.div`
  font-weight: 600;
  color: var(--dark-color);
`;

const SkillCategory = styled.div`
  font-size: 0.875rem;
  color: var(--text-muted);
`;

const StatusBadge = styled.span`
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  
  &.pending {
    background-color: #fff3cd;
    color: #856404;
  }
  
  &.approved {
    background-color: #d1edff;
    color: #0c5460;
  }
  
  &.rejected {
    background-color: #f8d7da;
    color: #721c24;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled.button`
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &.approve {
    background-color: #d1edff;
    color: #0c5460;
    
    &:hover {
      background-color: #b8e6ff;
    }
  }

  &.reject {
    background-color: #f8d7da;
    color: #721c24;
    
    &:hover {
      background-color: #f1aeb5;
    }
  }

  &.view {
    background-color: #e2e3e5;
    color: #495057;
    
    &:hover {
      background-color: #d1d3d4;
    }
  }
`;

const SkillModeration = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data for skills pending moderation
  const [skills] = useState([
    {
      id: 1,
      name: 'Advanced React Development',
      category: 'Programming',
      submittedBy: 'John Doe',
      submittedDate: '2024-01-15',
      status: 'pending'
    },
    {
      id: 2,
      name: 'Digital Marketing Strategy',
      category: 'Marketing',
      submittedBy: 'Jane Smith',
      submittedDate: '2024-01-14',
      status: 'approved'
    },
    {
      id: 3,
      name: 'Graphic Design Fundamentals',
      category: 'Design',
      submittedBy: 'Mike Johnson',
      submittedDate: '2024-01-13',
      status: 'rejected'
    }
  ]);

  const handleApprove = (skillId) => {
    console.log('Approving skill:', skillId);
    // Implementation for approving skill
  };

  const handleReject = (skillId) => {
    console.log('Rejecting skill:', skillId);
    // Implementation for rejecting skill
  };

  const handleView = (skillId) => {
    console.log('Viewing skill details:', skillId);
    // Implementation for viewing skill details
  };

  const filteredSkills = skills.filter(skill => {
    const matchesSearch = skill.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         skill.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         skill.submittedBy.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || skill.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <Container>
      <SectionTitle>
        <FontAwesomeIcon icon={faExclamationTriangle} />
        Skill Moderation
      </SectionTitle>

      <FilterBar>
        <SearchInput>
          <FontAwesomeIcon icon={faSearch} className="search-icon" />
          <input
            type="text"
            placeholder="Search skills, categories, or users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchInput>

        <FilterSelect
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="all">All Status</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
        </FilterSelect>
      </FilterBar>

      <SkillsTable>
        <TableHeader>
          <div>Skill</div>
          <div>Submitted By</div>
          <div>Date</div>
          <div>Status</div>
          <div>Reports</div>
          <div>Actions</div>
        </TableHeader>

        {filteredSkills.map(skill => (
          <TableRow key={skill.id}>
            <SkillInfo>
              <SkillName>{skill.name}</SkillName>
              <SkillCategory>{skill.category}</SkillCategory>
            </SkillInfo>
            <div>{skill.submittedBy}</div>
            <div>{skill.submittedDate}</div>
            <StatusBadge className={skill.status}>
              {skill.status.charAt(0).toUpperCase() + skill.status.slice(1)}
            </StatusBadge>
            <div>0</div>
            <ActionButtons>
              <ActionButton 
                className="view" 
                onClick={() => handleView(skill.id)}
                title="View Details"
              >
                <FontAwesomeIcon icon={faEye} />
              </ActionButton>
              {skill.status === 'pending' && (
                <>
                  <ActionButton 
                    className="approve" 
                    onClick={() => handleApprove(skill.id)}
                    title="Approve Skill"
                  >
                    <FontAwesomeIcon icon={faCheckCircle} />
                  </ActionButton>
                  <ActionButton 
                    className="reject" 
                    onClick={() => handleReject(skill.id)}
                    title="Reject Skill"
                  >
                    <FontAwesomeIcon icon={faTimesCircle} />
                  </ActionButton>
                </>
              )}
            </ActionButtons>
          </TableRow>
        ))}
      </SkillsTable>
    </Container>
  );
};

export default SkillModeration;
