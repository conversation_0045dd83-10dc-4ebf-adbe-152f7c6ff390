import { useState, useRef, useEffect } from "react";
import { Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUser,
  faSignOutAlt,
  faCog,
  faChevronDown,
  faTachometerAlt,
} from "@fortawesome/free-solid-svg-icons";

const UserDropdown = ({ user, onLogout }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const getInitials = (name) => {
    if (!name) return "U";
    return name.charAt(0).toUpperCase();
  };

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      {/* Dropdown Trigger */}
      <div
        className="flex items-center gap-2 cursor-pointer p-2 rounded transition-all duration-300 text-text hover:bg-black/5"
        onClick={toggleDropdown}
      >
        {/* User Avatar */}
        <div className="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center text-lg font-medium">
          {getInitials(user.name)}
        </div>

        {/* User Name - Hidden on mobile */}
        <span className="hidden md:block font-medium max-w-30 whitespace-nowrap overflow-hidden text-ellipsis">
          {user.name}
        </span>

        {/* Chevron Icon - Hidden on mobile */}
        <span
          className={`hidden md:block transition-transform duration-300 ${
            isOpen ? "rotate-180" : "rotate-0"
          }`}
        >
          <FontAwesomeIcon icon={faChevronDown} />
        </span>
      </div>

      {/* Dropdown Menu */}
      <div
        className={`
          absolute top-full right-0 w-48 bg-white rounded shadow-lg py-2 z-50
          transition-all duration-300
          ${
            isOpen
              ? "opacity-100 visible translate-y-0"
              : "opacity-0 invisible -translate-y-2"
          }
        `}
      >
        {/* Dashboard Link */}
        <Link
          to="/dashboard"
          className="px-4 py-3 flex items-center gap-3 text-text transition-all duration-300 hover:bg-black/5"
        >
          <FontAwesomeIcon
            icon={faTachometerAlt}
            className="text-primary w-4"
          />
          Dashboard
        </Link>

        {/* Profile Link */}
        <Link
          to="/profile"
          className="px-4 py-3 flex items-center gap-3 text-text transition-all duration-300 hover:bg-black/5"
        >
          <FontAwesomeIcon icon={faUser} className="text-primary w-4" />
          User Profile
        </Link>

        {/* Settings Link */}
        <Link
          to="/settings"
          className="px-4 py-3 flex items-center gap-3 text-text transition-all duration-300 hover:bg-black/5"
        >
          <FontAwesomeIcon icon={faCog} className="text-primary w-4" />
          Settings
        </Link>

        {/* Divider */}
        <div className="h-px bg-black/10 my-2" />

        {/* Logout Button */}
        <button
          onClick={onLogout}
          className="w-full px-4 py-3 flex items-center gap-3 text-text transition-all duration-300 hover:bg-black/5 text-left"
        >
          <FontAwesomeIcon icon={faSignOutAlt} className="text-error w-4" />
          Logout
        </button>
      </div>
    </div>
  );
};

export default UserDropdown;
