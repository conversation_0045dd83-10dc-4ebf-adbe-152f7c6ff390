import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUser,
  faSignOutAlt,
  faCog,
  faChevronDown,
  faTachometerAlt
} from '@fortawesome/free-solid-svg-icons';

const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const DropdownTrigger = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: var(--text-color);

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
`;

const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
`;

const UserName = styled.span`
  font-weight: 500;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: none;
`;

const ChevronIcon = styled.span`
  transition: transform 0.3s ease;
  transform: ${props => props.isOpen ? 'rotate(180deg)' : 'rotate(0)'};
  display: none;
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  z-index: 1000;
  opacity: ${props => props.isOpen ? 1 : 0};
  visibility: ${props => props.isOpen ? 'visible' : 'hidden'};
  transform: ${props => props.isOpen ? 'translateY(0)' : 'translateY(-10px)'};
  transition: all 0.3s ease;
`;

const DropdownItem = styled.div`
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-color);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  svg {
    color: ${props => props.danger ? '#dc3545' : 'var(--primary-color)'};
    width: 16px;
  }
`;

const DropdownDivider = styled.div`
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0.5rem 0;
`;

const UserDropdown = ({ user, onLogout }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getInitials = (name) => {
    if (!name) return 'U';
    return name.charAt(0).toUpperCase();
  };

  return (
    <DropdownContainer ref={dropdownRef}>
      <DropdownTrigger onClick={toggleDropdown}>
        <UserAvatar>
          {getInitials(user.name)}
        </UserAvatar>
        <UserName>{user.name}</UserName>
        <ChevronIcon isOpen={isOpen}>
          <FontAwesomeIcon icon={faChevronDown} />
        </ChevronIcon>
      </DropdownTrigger>

      <DropdownMenu isOpen={isOpen}>
        <DropdownItem as={Link} to="/dashboard">
          <FontAwesomeIcon icon={faTachometerAlt} />
          Dashboard
        </DropdownItem>

        <DropdownItem as={Link} to="/profile">
          <FontAwesomeIcon icon={faUser} />
          User Profile
        </DropdownItem>

        <DropdownItem as={Link} to="/settings">
          <FontAwesomeIcon icon={faCog} />
          Settings
        </DropdownItem>

        <DropdownDivider />

        <DropdownItem onClick={onLogout} danger>
          <FontAwesomeIcon icon={faSignOutAlt} />
          Logout
        </DropdownItem>
      </DropdownMenu>
    </DropdownContainer>
  );
};

export default UserDropdown;
