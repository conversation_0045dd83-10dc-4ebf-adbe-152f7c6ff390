import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

/**
 * AdminCard - Reusable card component for admin dashboard
 * 
 * @param {Object} props
 * @param {string} props.title - Card title
 * @param {string} props.subtitle - Card subtitle (optional)
 * @param {React.ReactNode} props.children - Card content
 * @param {Object} props.icon - FontAwesome icon (optional)
 * @param {string} props.className - Additional CSS classes
 * @param {Function} props.onClick - Click handler (optional)
 */
const AdminCard = ({ 
  title, 
  subtitle, 
  children, 
  icon, 
  className = '', 
  onClick,
  ...props 
}) => {
  const baseClasses = `
    bg-white rounded-lg shadow-sm border border-gray-200 p-6 
    transition-all duration-200 hover:shadow-md
    ${onClick ? 'cursor-pointer hover:border-blue-300' : ''}
    ${className}
  `;

  return (
    <div 
      className={baseClasses} 
      onClick={onClick}
      {...props}
    >
      {(title || icon) && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            {icon && (
              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                <FontAwesomeIcon icon={icon} className="text-blue-600" />
              </div>
            )}
            <div>
              {title && (
                <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-500">{subtitle}</p>
              )}
            </div>
          </div>
        </div>
      )}
      {children}
    </div>
  );
};

export default AdminCard;
