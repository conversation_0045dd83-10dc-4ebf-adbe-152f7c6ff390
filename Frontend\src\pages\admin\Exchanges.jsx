import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faFilter,
  faEdit,
  faTrash,
  faEye,
  faSort,
  faSortUp,
  faSortDown,
  faCheckCircle,
  faTimesCircle,
  faExchangeAlt,
  faCalendarAlt,
  faUsers
} from '@fortawesome/free-solid-svg-icons';

const Exchanges = () => {
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [statusFilter, setStatusFilter] = useState('all');

  const exchanges = [
    {
      id: 1,
      title: 'JavaScript Tutoring',
      skillCategory: 'Programming',
      teacher: {
        id: 101,
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
      },
      student: {
        id: 102,
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
      },
      status: 'completed',
      date: '2023-04-15',
      duration: '5 sessions',
      rating: 4.8
    },
    {
      id: 2,
      title: '<PERSON>ons',
      skillCategory: 'Music',
      teacher: {
        id: 103,
        name: 'Michael <PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
      },
      student: {
        id: 104,
        name: 'Sarah Wilson',
        avatar: 'https://randomuser.me/api/portraits/women/4.jpg'
      },
      status: 'in-progress',
      date: '2023-04-20',
      duration: '8 sessions',
      rating: null
    },
    {
      id: 3,
      title: 'Spanish Conversation',
      skillCategory: 'Language',
      teacher: {
        id: 105,
        name: 'David Brown',
        avatar: 'https://randomuser.me/api/portraits/men/5.jpg'
      },
      student: {
        id: 106,
        name: 'Lisa Garcia',
        avatar: 'https://randomuser.me/api/portraits/women/6.jpg'
      },
      status: 'scheduled',
      date: '2023-04-25',
      duration: '6 sessions',
      rating: null
    },
    {
      id: 4,
      title: 'UI/UX Design',
      skillCategory: 'Design',
      teacher: {
        id: 107,
        name: 'Anna Martinez',
        avatar: 'https://randomuser.me/api/portraits/women/7.jpg'
      },
      student: {
        id: 108,
        name: 'Robert Taylor',
        avatar: 'https://randomuser.me/api/portraits/men/8.jpg'
      },
      status: 'cancelled',
      date: '2023-04-10',
      duration: '4 sessions',
      rating: null
    }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'completed', label: 'Completed' },
    { value: 'in-progress', label: 'In Progress' },
    { value: 'scheduled', label: 'Scheduled' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const filteredExchanges = statusFilter === 'all' 
    ? exchanges 
    : exchanges.filter(exchange => exchange.status === statusFilter);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field) => {
    if (sortField !== field) {
      return <FontAwesomeIcon icon={faSort} className="text-gray-400" />;
    }
    return sortDirection === 'asc' 
      ? <FontAwesomeIcon icon={faSortUp} className="text-blue-600" />
      : <FontAwesomeIcon icon={faSortDown} className="text-blue-600" />;
  };

  const getStatusBadge = (status) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full flex items-center gap-1";
    switch (status.toLowerCase()) {
      case 'completed':
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800`}>
            <FontAwesomeIcon icon={faCheckCircle} />
            Completed
          </span>
        );
      case 'in-progress':
        return (
          <span className={`${baseClasses} bg-blue-100 text-blue-800`}>
            <FontAwesomeIcon icon={faExchangeAlt} />
            In Progress
          </span>
        );
      case 'scheduled':
        return (
          <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
            <FontAwesomeIcon icon={faCalendarAlt} />
            Scheduled
          </span>
        );
      case 'cancelled':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            <FontAwesomeIcon icon={faTimesCircle} />
            Cancelled
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800`}>
            {status}
          </span>
        );
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">Exchange Management</h1>
        <div className="flex items-center gap-4">
          <select 
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      {/* Filter Section */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-md">
          <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input 
            type="text" 
            placeholder="Search exchanges..." 
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
          <FontAwesomeIcon icon={faFilter} />
          Filters
        </button>
      </div>
      
      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th 
                  onClick={() => handleSort('title')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Exchange
                    {getSortIcon('title')}
                  </div>
                </th>
                <th 
                  onClick={() => handleSort('teacher')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Teacher
                    {getSortIcon('teacher')}
                  </div>
                </th>
                <th 
                  onClick={() => handleSort('student')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Student
                    {getSortIcon('student')}
                  </div>
                </th>
                <th 
                  onClick={() => handleSort('status')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Status
                    {getSortIcon('status')}
                  </div>
                </th>
                <th 
                  onClick={() => handleSort('date')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2">
                    Date
                    {getSortIcon('date')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredExchanges.map(exchange => (
                <tr key={exchange.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FontAwesomeIcon icon={faExchangeAlt} className="text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{exchange.title}</div>
                        <div className="text-sm text-gray-500">{exchange.skillCategory} • {exchange.duration}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <img 
                          className="h-8 w-8 rounded-full object-cover" 
                          src={exchange.teacher.avatar} 
                          alt={exchange.teacher.name} 
                        />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{exchange.teacher.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <img 
                          className="h-8 w-8 rounded-full object-cover" 
                          src={exchange.student.avatar} 
                          alt={exchange.student.name} 
                        />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{exchange.student.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(exchange.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(exchange.date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button className="text-blue-600 hover:text-blue-900 p-1" title="View">
                        <FontAwesomeIcon icon={faEye} />
                      </button>
                      <button className="text-green-600 hover:text-green-900 p-1" title="Edit">
                        <FontAwesomeIcon icon={faEdit} />
                      </button>
                      <button className="text-red-600 hover:text-red-900 p-1" title="Delete">
                        <FontAwesomeIcon icon={faTrash} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Exchanges;
