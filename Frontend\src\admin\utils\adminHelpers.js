/**
 * Admin Utility Functions
 * 
 * Collection of helper functions for admin operations
 */

/**
 * Format date for display in admin tables
 * @param {string|Date} date - Date to format
 * @param {Object} options - Intl.DateTimeFormat options
 * @returns {string} - Formatted date string
 */
export const formatAdminDate = (date, options = {}) => {
  const defaultOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  };
  
  try {
    return new Date(date).toLocaleDateString(undefined, { ...defaultOptions, ...options });
  } catch (error) {
    return 'Invalid Date';
  }
};

/**
 * Format numbers for display (e.g., user counts, ratings)
 * @param {number} number - Number to format
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted number string
 */
export const formatAdminNumber = (number, options = {}) => {
  const { 
    decimals = 0, 
    prefix = '', 
    suffix = '',
    useThousandsSeparator = true 
  } = options;
  
  if (typeof number !== 'number' || isNaN(number)) {
    return '0';
  }
  
  const formatted = useThousandsSeparator 
    ? number.toLocaleString(undefined, { minimumFractionDigits: decimals, maximumFractionDigits: decimals })
    : number.toFixed(decimals);
    
  return `${prefix}${formatted}${suffix}`;
};

/**
 * Generate status badge classes based on status
 * @param {string} status - Status value
 * @returns {string} - CSS classes for status badge
 */
export const getStatusBadgeClasses = (status) => {
  const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
  
  const statusMap = {
    active: `${baseClasses} bg-green-100 text-green-800`,
    inactive: `${baseClasses} bg-gray-100 text-gray-800`,
    pending: `${baseClasses} bg-yellow-100 text-yellow-800`,
    suspended: `${baseClasses} bg-red-100 text-red-800`,
    blocked: `${baseClasses} bg-red-100 text-red-800`,
    completed: `${baseClasses} bg-green-100 text-green-800`,
    'in-progress': `${baseClasses} bg-blue-100 text-blue-800`,
    scheduled: `${baseClasses} bg-yellow-100 text-yellow-800`,
    cancelled: `${baseClasses} bg-red-100 text-red-800`,
  };
  
  return statusMap[status?.toLowerCase()] || `${baseClasses} bg-gray-100 text-gray-800`;
};

/**
 * Generate avatar URL or initials
 * @param {Object} user - User object
 * @returns {Object} - Avatar data { type: 'image'|'initials', value: string }
 */
export const getAdminAvatar = (user) => {
  if (user?.avatar) {
    return { type: 'image', value: user.avatar };
  }
  
  if (user?.name) {
    const initials = user.name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
    return { type: 'initials', value: initials };
  }
  
  return { type: 'initials', value: '??' };
};

/**
 * Validate admin permissions
 * @param {string} userRole - User's role
 * @param {string} requiredPermission - Required permission
 * @param {Object} permissions - Permissions configuration
 * @returns {boolean} - Whether user has permission
 */
export const hasAdminPermission = (userRole, requiredPermission, permissions = {}) => {
  const rolePermissions = permissions[userRole]?.permissions || [];
  return rolePermissions.includes('*') || rolePermissions.includes(requiredPermission);
};

/**
 * Generate table column configuration
 * @param {Array} fields - Field definitions
 * @returns {Array} - Table column configuration
 */
export const generateTableColumns = (fields) => {
  return fields.map(field => ({
    key: field.key || field.field,
    field: field.field,
    title: field.title || field.field.charAt(0).toUpperCase() + field.field.slice(1),
    sortable: field.sortable !== false,
    render: field.render,
    className: field.className,
    cellClassName: field.cellClassName,
  }));
};

/**
 * Export data to CSV
 * @param {Array} data - Data to export
 * @param {string} filename - CSV filename
 * @param {Array} columns - Column definitions
 */
export const exportToCSV = (data, filename = 'admin-export.csv', columns = []) => {
  if (!data.length) return;
  
  const headers = columns.length 
    ? columns.map(col => col.title || col.field)
    : Object.keys(data[0]);
    
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      (columns.length ? columns.map(col => col.field) : Object.keys(row))
        .map(field => {
          const value = row[field];
          // Escape commas and quotes in CSV
          return typeof value === 'string' && (value.includes(',') || value.includes('"'))
            ? `"${value.replace(/"/g, '""')}"`
            : value;
        })
        .join(',')
    )
  ].join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

/**
 * Debounce function for search inputs
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

export default {
  formatAdminDate,
  formatAdminNumber,
  getStatusBadgeClasses,
  getAdminAvatar,
  hasAdminPermission,
  generateTableColumns,
  exportToCSV,
  debounce,
};
