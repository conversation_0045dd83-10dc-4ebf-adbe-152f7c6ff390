import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faListAlt,
  faCertificate,
  faUserShield,
  faCheckCircle,
  faTimesCircle,
  faBan,
  faEdit,
  faTrash,
  faEye,
  faFlag,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';

import Button from '../../components/ui/Button';
import UserManagement from './UserManagement';
import SkillModeration from './SkillModeration';

// Styled Components
const AdminContainer = styled.div`
  background-color: #f8f9fa;
  min-height: calc(100vh - 80px);
  padding: 2rem;
`;

const AdminHeader = styled.div`
  margin-bottom: 2rem;
`;

const AdminTitle = styled.h1`
  font-size: 2.5rem;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
`;

const AdminSubtitle = styled.p`
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 1.5rem;
`;

const AdminTabs = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
`;

const AdminTab = styled.button`
  padding: 1rem 1.5rem;
  background-color: ${props => props.active ? 'white' : 'transparent'};
  color: ${props => props.active ? 'var(--primary-color)' : 'var(--text-color)'};
  border: none;
  border-bottom: ${props => props.active ? '3px solid var(--primary-color)' : '3px solid transparent'};
  font-weight: ${props => props.active ? 'bold' : 'normal'};
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background-color: ${props => props.active ? 'white' : '#f1f1f1'};
    color: var(--primary-color);
  }
`;

const AdminContent = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
`;

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('users');

  return (
    <AdminContainer>
      <AdminHeader>
        <AdminTitle>Admin Dashboard</AdminTitle>
        <AdminSubtitle>Manage users, skill listings, and platform content</AdminSubtitle>
      </AdminHeader>

      <AdminTabs>
        <AdminTab 
          active={activeTab === 'users'} 
          onClick={() => setActiveTab('users')}
        >
          <FontAwesomeIcon icon={faUsers} />
          User Management
        </AdminTab>
        <AdminTab 
          active={activeTab === 'skills'} 
          onClick={() => setActiveTab('skills')}
        >
          <FontAwesomeIcon icon={faListAlt} />
          Skill Listings
        </AdminTab>
      </AdminTabs>

      <AdminContent>
        {activeTab === 'users' && <UserManagement />}
        {activeTab === 'skills' && <SkillModeration />}
      </AdminContent>
    </AdminContainer>
  );
};

export default AdminDashboard;
