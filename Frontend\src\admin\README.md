# Admin Module

This directory contains all admin-related components, pages, utilities, and configurations for the SkillSwap application. The module is built with Tailwind CSS and follows modern React patterns and industry standards.

## 📁 Structure

```
admin/
├── components/          # Reusable admin UI components
│   ├── AdminCard.jsx   # Card component for admin dashboard
│   ├── AdminTable.jsx  # Table component with sorting/filtering
│   ├── AdminStats.jsx  # Statistics display component
│   └── index.js        # Component exports
├── hooks/              # Custom hooks for admin functionality
│   └── useAdminData.js # Data management hook
├── utils/              # Utility functions
│   └── adminHelpers.js # Helper functions for admin operations
├── index.js            # Main module exports
└── README.md           # This file
```

## 🚀 Quick Start

### Basic Usage

```jsx
// Import the main admin layout
import { AdminLayout } from '../admin';

// Import admin pages
import { Users, Skills, Exchanges } from '../admin';

// Import admin components
import { AdminCard, AdminTable, AdminStats } from '../admin';

// Import utilities
import { useAdminData, formatAdminDate, getStatusBadgeClasses } from '../admin';
```

### Using Admin Components

```jsx
// AdminCard Example
<AdminCard 
  title="User Statistics" 
  subtitle="Overview of user activity"
  icon={faUsers}
>
  <p>Card content goes here</p>
</AdminCard>

// AdminTable Example
<AdminTable
  columns={[
    { field: 'name', title: 'Name', sortable: true },
    { field: 'email', title: 'Email', sortable: true },
    { field: 'status', title: 'Status', render: (value) => <StatusBadge status={value} /> }
  ]}
  data={users}
  sorting={{ field: 'name', direction: 'asc' }}
  onSort={handleSort}
/>

// AdminStats Example
<AdminStats
  title="Total Users"
  value="1,234"
  change="+12%"
  changeType="increase"
  icon={faUsers}
  iconColor="bg-blue-500"
/>
```

### Using Admin Hooks

```jsx
import { useAdminData } from '../admin';

const MyAdminPage = () => {
  const {
    data,
    loading,
    handleSort,
    handleSearch,
    handlePageChange,
    pagination
  } = useAdminData(initialData, {
    defaultSortField: 'name',
    searchFields: ['name', 'email'],
    itemsPerPage: 10
  });

  return (
    <div>
      <input 
        type="text" 
        onChange={(e) => handleSearch(e.target.value)}
        placeholder="Search..."
      />
      <AdminTable 
        data={data}
        onSort={handleSort}
        // ... other props
      />
    </div>
  );
};
```

## 🎨 Styling with Tailwind CSS

All admin components are built with Tailwind CSS classes. The design system includes:

### Color Palette
- **Primary**: Blue (`blue-600`, `blue-500`, etc.)
- **Success**: Green (`green-600`, `green-100`, etc.)
- **Warning**: Yellow (`yellow-600`, `yellow-100`, etc.)
- **Danger**: Red (`red-600`, `red-100`, etc.)
- **Neutral**: Gray (`gray-900`, `gray-500`, etc.)

### Common Patterns
- **Cards**: `bg-white rounded-lg shadow-sm border border-gray-200`
- **Buttons**: `bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg`
- **Status Badges**: `px-2 py-1 text-xs font-medium rounded-full`

## 🔧 Utilities

### Date Formatting
```jsx
import { formatAdminDate } from '../admin';

const formattedDate = formatAdminDate('2023-12-01'); // "Dec 1, 2023"
```

### Status Badges
```jsx
import { getStatusBadgeClasses } from '../admin';

const badgeClasses = getStatusBadgeClasses('active'); // Returns appropriate Tailwind classes
```

### Data Export
```jsx
import { exportToCSV } from '../admin';

exportToCSV(userData, 'users-export.csv', columns);
```

## 🔐 Permissions

The admin module includes a permission system:

```jsx
import { hasAdminPermission, adminPermissions } from '../admin';

const canManageUsers = hasAdminPermission(
  userRole, 
  'users.manage', 
  adminPermissions
);
```

### Available Permissions
- `users.manage` - Full user management
- `skills.manage` - Full skill management
- `exchanges.manage` - Full exchange management
- `skills.moderate` - Skill moderation only
- `exchanges.moderate` - Exchange moderation only

## 📱 Responsive Design

All admin components are fully responsive and work across different screen sizes:

- **Desktop**: Full sidebar layout
- **Tablet**: Collapsible sidebar
- **Mobile**: Overlay sidebar with mobile-optimized tables

## 🧪 Testing

When testing admin components, consider:

1. **Permission-based rendering**
2. **Data sorting and filtering**
3. **Responsive behavior**
4. **Loading states**
5. **Error handling**

## 🚀 Performance

The admin module is optimized for performance:

- **Lazy loading** for admin pages
- **Memoized components** where appropriate
- **Efficient data filtering** and sorting
- **Minimal re-renders** with proper state management

## 📝 Contributing

When adding new admin components:

1. Follow the existing file structure
2. Use Tailwind CSS for styling
3. Include proper TypeScript/PropTypes definitions
4. Add to the appropriate index.js file
5. Update this README if needed

## 🔗 Related Files

- `../components/admin/AdminLayout.jsx` - Main admin layout
- `../pages/admin/` - Admin page components
- `../context/AuthContext.jsx` - Authentication context
- `tailwind.config.js` - Tailwind configuration
