import { forwardRef } from "react";

const Button = forwardRef(
  (
    {
      variant = "primary",
      size = "medium",
      fullWidth = false,
      children,
      className = "",
      disabled = false,
      ...props
    },
    ref
  ) => {
    // Base button classes
    const baseClasses = `
    inline-flex items-center justify-center
    font-semibold rounded-md border-none
    transition-all duration-300 ease
    cursor-pointer
    disabled:opacity-60 disabled:cursor-not-allowed
    focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
  `;

    // Size variants
    const sizeClasses = {
      small: "px-4 py-2 text-sm",
      medium: "px-6 py-3 text-md",
      large: "px-8 py-4 text-lg",
    };

    // Variant styles
    const variantClasses = {
      primary: `
      bg-primary text-white
      hover:brightness-110 hover:-translate-y-0.5 hover:shadow-button
      active:translate-y-0
      disabled:hover:brightness-100 disabled:hover:translate-y-0 disabled:hover:shadow-none
    `,
      secondary: `
      bg-secondary text-dark
      hover:brightness-105 hover:-translate-y-0.5 hover:shadow-button
      active:translate-y-0
      disabled:hover:brightness-100 disabled:hover:translate-y-0 disabled:hover:shadow-none
    `,
      outline: `
      bg-transparent text-primary border-2 border-primary
      hover:bg-primary hover:text-white hover:-translate-y-0.5 hover:shadow-button
      active:translate-y-0
      disabled:hover:bg-transparent disabled:hover:text-primary disabled:hover:translate-y-0 disabled:hover:shadow-none
    `,
      text: `
      bg-transparent text-primary px-3 py-2
      hover:bg-primary/10
      disabled:hover:bg-transparent
    `,
    };

    // Width classes
    const widthClasses = fullWidth ? "w-full" : "";

    // Combine all classes
    const combinedClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${widthClasses}
    ${className}
  `
      .replace(/\s+/g, " ")
      .trim();

    return (
      <button
        ref={ref}
        className={combinedClasses}
        disabled={disabled}
        {...props}
      >
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
