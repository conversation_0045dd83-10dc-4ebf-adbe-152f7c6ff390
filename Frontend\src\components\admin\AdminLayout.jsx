import { useState } from "react";
import {
  Outlet,
  Link,
  useLocation,
  Navigate,
  useNavigate,
} from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import UserDropdown from "../ui/UserDropdown";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUsers,
  faGraduationCap,
  faExchangeAlt,
  faBars,
  faTimes,
  faBell,
} from "@fortawesome/free-solid-svg-icons";

const AdminLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();
  const { currentUser, isAdmin, logout, loading } = useAuth();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!currentUser || !isAdmin()) {
    return <Navigate to="/login" replace />;
  }

  const navItems = [
    {
      to: "/admin/users",
      icon: faUsers,
      label: "Users",
      isActive: location.pathname === "/admin" || location.pathname.startsWith("/admin/users"),
    },
    {
      to: "/admin/skills",
      icon: faGraduationCap,
      label: "Skills",
      isActive: location.pathname.startsWith("/admin/skills"),
    },
    {
      to: "/admin/exchanges",
      icon: faExchangeAlt,
      label: "Exchanges",
      isActive: location.pathname.startsWith("/admin/exchanges"),
    },
  ];

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar */}
      <aside
        className={`
          fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 text-white transform transition-transform duration-300 ease-in-out
          ${sidebarOpen ? "translate-x-0" : "-translate-x-full"}
          md:translate-x-0 md:static md:inset-0
        `}
      >
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-5 border-b border-gray-700">
          <Link
            to="/admin"
            className="flex items-center text-xl font-bold text-white no-underline hover:text-gray-200"
          >
            Skill<span className="text-blue-500">Swap</span> Admin
          </Link>
          <button
            onClick={toggleSidebar}
            className="text-white hover:text-gray-300 md:hidden"
          >
            <FontAwesomeIcon icon={faTimes} className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="py-5">
          {navItems.map((item) => (
            <Link
              key={item.to}
              to={item.to}
              className={`
                flex items-center px-5 py-3 text-gray-300 no-underline transition-all duration-200 border-l-3 border-transparent
                hover:bg-white/10 hover:text-white
                ${item.isActive ? "bg-white/10 text-white border-l-blue-500" : ""}
              `}
            >
              <FontAwesomeIcon icon={item.icon} className="w-5 h-5 mr-3" />
              {item.label}
            </Link>
          ))}
        </nav>
      </aside>

      {/* Main Content */}
      <main
        className={`
          flex-1 transition-all duration-300 ease-in-out
          ${sidebarOpen ? "md:ml-64" : "md:ml-0"}
          md:w-auto w-full
        `}
      >
        {/* Header */}
        <header className="sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-5 py-4">
            <button
              onClick={toggleSidebar}
              className="text-gray-600 hover:text-gray-900 md:hidden"
            >
              <FontAwesomeIcon icon={faBars} className="w-5 h-5" />
            </button>

            <div className="flex items-center space-x-4">
              <button className="relative text-gray-600 hover:text-blue-600 transition-colors">
                <FontAwesomeIcon icon={faBell} className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  3
                </span>
              </button>
              <UserDropdown user={currentUser} onLogout={handleLogout} />
            </div>
          </div>
        </header>

        {/* Content */}
        <div className="p-5">
          <Outlet />
        </div>
      </main>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
          onClick={toggleSidebar}
        />
      )}
    </div>
  );
};

export default AdminLayout;
